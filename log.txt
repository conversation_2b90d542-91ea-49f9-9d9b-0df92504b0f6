2025-08-26 09:25:49.145502: 61
2025-08-26 09:25:49.149502: ✅ 数据库更新成功: planConfig
2025-08-26 09:25:49.149502: ✅ 数据库更新成功: SysConfig
2025-08-26 09:25:49.150502: ✅ 数据库更新成功: Sip2Config
2025-08-26 09:25:49.150502: ✅ 数据库更新成功: pageConfig
2025-08-26 09:25:49.150502: ✅ 数据库更新成功: readerConfig
2025-08-26 09:25:49.150502: ✅ 数据库更新成功: banZhengConfig
2025-08-26 09:25:49.150502: ✅ 数据库更新成功: printConfig
2025-08-26 09:25:49.365499: 开始初始化闸机协调器...
2025-08-26 09:25:49.366499: ✅ 已清除串口配置缓存，下次访问将重新从数据库读取
2025-08-26 09:25:49.366499: ✅ 已清除 SettingProvider 串口配置缓存
2025-08-26 09:25:49.381498: ✅ 通过 getSerialConfig 获取串口配置: COM1 @ 115200
2025-08-26 09:25:49.381498: ✅ 串口配置加载完成: COM1 @ 115200
2025-08-26 09:25:49.381498: 可用串口: [COM1, COM2, COM3, COM4, COM5, COM6]
2025-08-26 09:25:49.384497: 连接闸机串口: COM1
2025-08-26 09:25:49.384497: 尝试连接串口: COM1, 波特率: 115200
2025-08-26 09:25:49.384497: 串口连接成功: COM1 at 115200 baud
2025-08-26 09:25:49.385498: 开始监听串口数据
2025-08-26 09:25:49.385498: 串口连接状态变化: true
2025-08-26 09:25:49.385498: 闸机串口连接成功
2025-08-26 09:25:49.385498: 串口 COM1 连接成功 (波特率: 115200)
2025-08-26 09:25:49.385498: 闸机串口服务初始化成功: COM1
2025-08-26 09:25:49.385498: 开始监听串口数据（通过 GateSerialManager 事件流）
2025-08-26 09:25:49.385498: 开始监听闸机串口命令
2025-08-26 09:25:49.385498: 开始初始化RFID服务和共享池...
2025-08-26 09:25:49.386498: 开始初始化增强RFID服务...
2025-08-26 09:25:49.386498: 开始初始化增强RFID服务...
2025-08-26 09:25:49.386498: SIP2图书信息服务初始化完成
2025-08-26 09:25:49.386498: 增强RFID服务初始化完成，配置了1个阅读器
2025-08-26 09:25:49.387498: 📋 从数据库读取主从机配置: channel_1
2025-08-26 09:25:49.387498: 📋 配置详情: 主机模式
2025-08-26 09:25:49.387498: 🚀 主机模式：启动RFID硬件持续扫描
2025-08-26 09:25:49.387498: 启动RFID持续扫描...
2025-08-26 09:25:49.387498: changeReaders
2025-08-26 09:25:49.387498: createIsolate isOpen:false,isOpening:false
2025-08-26 09:25:49.388498: createIsolate newport null
2025-08-26 09:25:49.413499: socket 连接成功,isBroadcast:false
2025-08-26 09:25:49.413499: changeSocketStatus:true
2025-08-26 09:25:49.413499: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-26 09:25:49.414497: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-26 09:25:49.515496: Rsp : 941AY1AZFDFC
2025-08-26 09:25:49.527495: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-26 09:25:49.528496: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-26 09:25:49.528496: 发送心跳
2025-08-26 09:25:49.528496: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-26 09:25:49.889492: 找到网口配置: LSGate图书馆安全门RFID阅读器 - **************:6012
2025-08-26 09:25:49.890490: 使用网口连接: **************:6012
2025-08-26 09:25:49.890490: open():SendPort
2025-08-26 09:25:49.891490: untilDetcted():SendPort
2025-08-26 09:25:49.891490: 网口连接成功: **************:6012
2025-08-26 09:25:49.891490: startInventory():SendPort
2025-08-26 09:25:49.892493: RFID硬件扫描已启动，阅读器开始持续工作
2025-08-26 09:25:49.892493: RFID持续扫描启动完成
2025-08-26 09:25:49.893490: 增强RFID服务初始化完成，持续扫描已启动
2025-08-26 09:25:49.893490: 📋 从数据库读取主从机配置: channel_1
2025-08-26 09:25:49.894490: 📋 配置详情: 主机模式
2025-08-26 09:25:49.894490: 🚀 主机模式：启动持续数据收集，数据将持续进入共享池
2025-08-26 09:25:49.894490: 🎯 关键修复：使用轮询机制确保标签持续被发现
2025-08-26 09:25:49.894490: 🧹 清空RFID缓冲区（保持tagList），确保数据收集正常工作...
2025-08-26 09:25:49.894490: 清空RFID扫描缓冲区...
2025-08-26 09:25:49.895489: 跳过HWTagProvider清空，保持标签列表用于轮询
2025-08-26 09:25:49.895489: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 09:25:49.895489: 🔄 开始重置已处理条码集合...
2025-08-26 09:25:49.895489: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 09:25:49.895489: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 09:25:49.895489: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:25:49.896489: 📊 当前tagList状态: 0个标签
2025-08-26 09:25:49.896489: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-26 09:25:49.896489: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 09:25:49.896489: 🚀 启动增强数据收集（事件监听 + 轮询备用）...
2025-08-26 09:25:49.897489: 🚀 开始RFID数据收集...
2025-08-26 09:25:49.897489: 📋 扫描结果和缓存已清空
2025-08-26 09:25:49.897489: 清空RFID扫描缓冲区...
2025-08-26 09:25:49.897489: 跳过HWTagProvider清空，保持标签列表用于轮询
2025-08-26 09:25:49.897489: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 09:25:49.897489: 🔄 开始重置已处理条码集合...
2025-08-26 09:25:49.897489: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 09:25:49.897489: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 09:25:49.898489: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:25:49.898489: 📊 当前tagList状态: 0个标签
2025-08-26 09:25:49.898489: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-26 09:25:49.898489: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 09:25:49.898489: 🎯 启动数据监听和轮询机制...
2025-08-26 09:25:49.898489: 🚀 标签轮询机制已启动 (每500ms轮询一次)
2025-08-26 09:25:49.898489: ✅ 标签监听改为仅轮询机制（每500ms轮询tagList）
2025-08-26 09:25:49.899489: 📊 当前HWTagProvider状态:
2025-08-26 09:25:49.899489:   - tagList: 0个标签
2025-08-26 09:25:49.899489:   - type: null
2025-08-26 09:25:49.899489: 🎯 标签数据获取已统一为轮询机制
2025-08-26 09:25:49.899489: ✅ RFID数据收集已启动，轮询机制运行中
2025-08-26 09:25:49.899489: 📊 当前tagList状态: 0个标签
2025-08-26 09:25:49.899489: subThread :ReaderCommand.readerList
2025-08-26 09:25:49.899489: commandRsp:ReaderCommand.readerList
2025-08-26 09:25:49.899489: readerList：1,readerSetting：1
2025-08-26 09:25:49.900489: cacheUsedReaders:1
2025-08-26 09:25:49.900489: subThread :ReaderCommand.open
2025-08-26 09:25:49.900489: commandRsp:ReaderCommand.open
2025-08-26 09:25:49.900489: LSGate使用网络连接 IP: **************, Port: 6012, DeviceType: LSGControlCenter
2025-08-26 09:25:49.900489: Rsp : 98YYYNNN00500320250826    0925432.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD51A
2025-08-26 09:25:49.901489: LSGate device opened successfully, handle: 1495381146272
2025-08-26 09:25:49.901489: open reader readerType ：22 ret：0
2025-08-26 09:25:49.901489: [[22, 0]]
2025-08-26 09:25:49.901489: changeType:ReaderErrorType.openSuccess
2025-08-26 09:25:49.901489: subThread :ReaderCommand.untilDetected
2025-08-26 09:25:49.902488: commandRsp:ReaderCommand.untilDetected
2025-08-26 09:25:49.902488: subThread :ReaderCommand.startInventory
2025-08-26 09:25:49.902488: commandRsp:ReaderCommand.startInventory
2025-08-26 09:25:50.000491: 🔄 执行首次轮询...
2025-08-26 09:25:50.000491: 🔄 开始RFID轮询检查...
2025-08-26 09:25:50.001489: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:50.001489: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:50.400481: 🔄 开始RFID轮询检查...
2025-08-26 09:25:50.400481: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:50.401481: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:50.504481: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:50.505485:   - 设备句柄: 1495381146272
2025-08-26 09:25:50.506480:   - FetchRecords返回值: 0
2025-08-26 09:25:50.506480:   - 报告数量: 0
2025-08-26 09:25:50.506480: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:50.507480:   - 发现标签数量: 0
2025-08-26 09:25:50.507480:   - 未发现任何RFID标签
2025-08-26 09:25:50.507480: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:50.508479: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:50.900472: 🔍 验证数据收集状态...
2025-08-26 09:25:50.901472: 📊 当前tagList: 0个标签
2025-08-26 09:25:50.901472: ⚠️ tagList为空，等待RFID硬件扫描到标签
2025-08-26 09:25:50.901472: ✅ 主机持续数据收集已启动，共享池将持续接收RFID数据
2025-08-26 09:25:50.901472: 🔄 轮询机制每500ms检查一次tagList，确保标签不会丢失
2025-08-26 09:25:50.901472: 开始全局初始化共享扫描池服务...
2025-08-26 09:25:50.901472: 共享扫描池已集成现有RFID服务
2025-08-26 09:25:50.902472: 📡 初始化后RFID扫描状态: scanning=false
2025-08-26 09:25:50.902472: 🔄 开始重置已处理条码集合...
2025-08-26 09:25:50.902472: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 09:25:50.902472: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 09:25:50.902472: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:25:50.902472: 📊 当前tagList状态: 0个标签
2025-08-26 09:25:50.902472: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:25:50.902472: 🔄 开始RFID轮询检查...
2025-08-26 09:25:50.903471: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:50.903471: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:50.903471: 🔄 开始RFID轮询检查...
2025-08-26 09:25:50.903471: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:50.903471: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:50.903471: 共享扫描池服务全局初始化完成
2025-08-26 09:25:50.903471: 🚀 初始化新架构服务...
2025-08-26 09:25:50.904471: 🖥️ 主机模式：使用主机集合A服务
2025-08-26 09:25:50.904471: ⏹️ 书籍信息查询服务停止监听
2025-08-26 09:25:50.904471: 🚀 书籍信息查询服务开始监听集合A变化
2025-08-26 09:25:50.904471: ✅ 新架构服务初始化完成
2025-08-26 09:25:50.904471: RFID服务和共享池初始化完成，持续扫描已启动
2025-08-26 09:25:50.904471: 闸机协调器初始化完成
2025-08-26 09:25:50.904471: 🔧 开始初始化主从机扩展（使用持久化配置）...
2025-08-26 09:25:50.904471: 开始初始化主从机扩展...
2025-08-26 09:25:50.904471: 从 seasetting 数据库加载主从机配置成功: channel_1
2025-08-26 09:25:50.904471: 配置详情: 主机模式
2025-08-26 09:25:50.905471: 📡 从 SettingProvider 获取串口配置: COM1 @ 115200
2025-08-26 09:25:50.905471: ✅ 通过 SettingProvider 加载串口配置成功
2025-08-26 09:25:50.905471: 启用主从机扩展: channel_1 (主机)
2025-08-26 09:25:50.905471: ✅ 数据变化通知流已创建
2025-08-26 09:25:50.905471: 共享扫描池已集成现有RFID服务
2025-08-26 09:25:50.906471: 📡 初始化后RFID扫描状态: scanning=false
2025-08-26 09:25:50.906471: 🔄 开始重置已处理条码集合...
2025-08-26 09:25:50.906471: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 09:25:50.906471: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 09:25:50.906471: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:25:50.906471: 📊 当前tagList状态: 0个标签
2025-08-26 09:25:50.906471: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:25:50.906471: 🔄 开始RFID轮询检查...
2025-08-26 09:25:50.906471: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:50.906471: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:50.907471: 配置为主机模式，监听端口: 8888
2025-08-26 09:25:50.907471: 主机服务器启动成功，监听端口: 8888
2025-08-26 09:25:50.907471: 主机模式配置完成（请求-响应模式）
2025-08-26 09:25:50.908471: [channel_1] 已集成现有GateCoordinator，开始监听事件
2025-08-26 09:25:50.908471: 主从机扩展启用成功
2025-08-26 09:25:50.908471: 主从机扩展初始化完成
2025-08-26 09:25:50.908471: 配置信息: MasterSlaveConfig(channelId: channel_1, isMaster: true, slaveAddress: null, masterAddress: null, port: 8888)
2025-08-26 09:25:50.908471: ✅ 加载到持久化配置: 主机模式, 通道: channel_1
2025-08-26 09:25:50.908471: 主从机扩展初始化完成
2025-08-26 09:25:50.908471: 安全闸机系统初始化完成
2025-08-26 09:25:50.908471: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:50.909471:   - 设备句柄: 1495381146272
2025-08-26 09:25:50.909471:   - FetchRecords返回值: 0
2025-08-26 09:25:50.909471:   - 报告数量: 0
2025-08-26 09:25:50.909471: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:50.909471:   - 发现标签数量: 0
2025-08-26 09:25:50.909471:   - 未发现任何RFID标签
2025-08-26 09:25:50.909471: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:50.909471: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:50.918471: 开始初始化MultiAuthManager...
2025-08-26 09:25:50.918471: 多认证管理器状态变更: initializing
2025-08-26 09:25:50.918471: 认证优先级管理器: 开始加载认证方式
2025-08-26 09:25:50.918471: 配置的排序: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-26 09:25:50.919471: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-26 09:25:50.919471: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-26 09:25:50.919471: 认证优先级管理器: 最终排序结果: 读者证
2025-08-26 09:25:50.919471: 认证优先级管理器: 主要认证方式: 读者证
2025-08-26 09:25:50.919471: 多认证管理器: 从优先级管理器加载的认证方式: 读者证
2025-08-26 09:25:50.919471: 多认证管理器: 当前默认显示方式: 读者证
2025-08-26 09:25:50.919471: 初始化读卡器认证服务
2025-08-26 09:25:50.920471: 读卡器认证服务初始化成功
2025-08-26 09:25:50.920471: 初始化共享读卡器认证服务
2025-08-26 09:25:50.920471: 读者证 认证服务初始化成功
2025-08-26 09:25:50.920471: 认证服务初始化完成，共初始化 1 种认证方式
2025-08-26 09:25:50.920471: 多认证管理器状态变更: idle
2025-08-26 09:25:50.920471: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.readerCard]
2025-08-26 09:25:50.920471: MultiAuthManager初始化完成
2025-08-26 09:25:50.921471: 开始初始化SilencePageViewModel...
2025-08-26 09:25:50.921471: 闸机串口服务已经初始化
2025-08-26 09:25:50.921471: 开始初始化闸机认证服务...
2025-08-26 09:25:50.921471: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-26 09:25:50.921471: RFID服务已经初始化
2025-08-26 09:25:50.921471: SIP2图书信息服务初始化完成
2025-08-26 09:25:50.921471: 💡 主从机扩展已准备就绪，请通过配置页面手动启用
2025-08-26 09:25:50.921471: 💡 可以通过MasterSlaveConfigPage进行配置
2025-08-26 09:25:50.922471: ✅ 统一事件监听已设置：SilencePageViewModel → GateCoordinator.eventStream
2025-08-26 09:25:50.922471: 串口监听已经启动
2025-08-26 09:25:50.922471: SilencePageViewModel初始化完成
2025-08-26 09:25:51.266465: dispose IndexPage
2025-08-26 09:25:51.267467: IndexPage dispose
2025-08-26 09:25:51.400463: 🔄 开始RFID轮询检查...
2025-08-26 09:25:51.400463: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:51.401465: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:51.402464: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:51.402464:   - 设备句柄: 1495381146272
2025-08-26 09:25:51.403464:   - FetchRecords返回值: 0
2025-08-26 09:25:51.403464:   - 报告数量: 0
2025-08-26 09:25:51.404463: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:51.404463:   - 发现标签数量: 0
2025-08-26 09:25:51.404463:   - 未发现任何RFID标签
2025-08-26 09:25:51.405464: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:51.405464: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:51.423465: 🔍 主从机模式检测: 启用=true, 主机模式=true
2025-08-26 09:25:51.423465: 🔍 扩展详细状态: {enabled: true, channel_id: channel_1, is_master: true, data_stream_ready: true, data_stream_exists: true, data_stream_closed: false, shared_pool_size: 0, queue_size: 0, comm_connected: false, timestamp: 2025-08-26T09:25:51.423465}
2025-08-26 09:25:51.424468: 🎯 检测到主机模式，无需设置数据监听
2025-08-26 09:25:51.900454: 🔄 开始RFID轮询检查...
2025-08-26 09:25:51.900454: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:51.900454: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:51.901454: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:51.901454:   - 设备句柄: 1495381146272
2025-08-26 09:25:51.901454:   - FetchRecords返回值: 0
2025-08-26 09:25:51.901454:   - 报告数量: 0
2025-08-26 09:25:51.902454: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:51.902454:   - 发现标签数量: 0
2025-08-26 09:25:51.902454:   - 未发现任何RFID标签
2025-08-26 09:25:51.902454: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:51.903454: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:52.401446: 🔄 开始RFID轮询检查...
2025-08-26 09:25:52.402447: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:52.402447: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:52.402447: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:52.403450:   - 设备句柄: 1495381146272
2025-08-26 09:25:52.403450:   - FetchRecords返回值: 0
2025-08-26 09:25:52.404446:   - 报告数量: 0
2025-08-26 09:25:52.404446: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:52.404446:   - 发现标签数量: 0
2025-08-26 09:25:52.405446:   - 未发现任何RFID标签
2025-08-26 09:25:52.405446: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:52.405446: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:52.900437: 🔄 开始RFID轮询检查...
2025-08-26 09:25:52.901441: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:52.901441: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:52.902440: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:52.903440:   - 设备句柄: 1495381146272
2025-08-26 09:25:52.903440:   - FetchRecords返回值: 0
2025-08-26 09:25:52.904438:   - 报告数量: 0
2025-08-26 09:25:52.904438: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:52.904438:   - 发现标签数量: 0
2025-08-26 09:25:52.905446:   - 未发现任何RFID标签
2025-08-26 09:25:52.905446: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:52.906437: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:53.401428: 🔄 开始RFID轮询检查...
2025-08-26 09:25:53.401428: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:53.402429: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:53.402429: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:53.403429:   - 设备句柄: 1495381146272
2025-08-26 09:25:53.403429:   - FetchRecords返回值: 0
2025-08-26 09:25:53.404429:   - 报告数量: 0
2025-08-26 09:25:53.404429: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:53.404429:   - 发现标签数量: 0
2025-08-26 09:25:53.405429:   - 未发现任何RFID标签
2025-08-26 09:25:53.405429: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:53.405429: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:53.900420: 🔄 开始RFID轮询检查...
2025-08-26 09:25:53.900420: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:53.901420: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:53.901420: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:53.902420:   - 设备句柄: 1495381146272
2025-08-26 09:25:53.902420:   - FetchRecords返回值: 0
2025-08-26 09:25:53.902420:   - 报告数量: 0
2025-08-26 09:25:53.903421: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:53.903421:   - 发现标签数量: 0
2025-08-26 09:25:53.904420:   - 未发现任何RFID标签
2025-08-26 09:25:53.904420: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:53.905420: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:54.401412: 🔄 开始RFID轮询检查...
2025-08-26 09:25:54.402414: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:54.402414: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:54.403412: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:54.403412:   - 设备句柄: 1495381146272
2025-08-26 09:25:54.403412:   - FetchRecords返回值: 0
2025-08-26 09:25:54.403412:   - 报告数量: 0
2025-08-26 09:25:54.404412: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:54.404412:   - 发现标签数量: 0
2025-08-26 09:25:54.404412:   - 未发现任何RFID标签
2025-08-26 09:25:54.404412: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:54.405411: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:54.900402: 🔄 开始RFID轮询检查...
2025-08-26 09:25:54.900402: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:54.901403: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:54.901403: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:54.902402:   - 设备句柄: 1495381146272
2025-08-26 09:25:54.902402:   - FetchRecords返回值: 0
2025-08-26 09:25:54.902402:   - 报告数量: 0
2025-08-26 09:25:54.903403: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:54.903403:   - 发现标签数量: 0
2025-08-26 09:25:54.903403:   - 未发现任何RFID标签
2025-08-26 09:25:54.904402: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:54.904402: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:55.401394: 🔄 开始RFID轮询检查...
2025-08-26 09:25:55.401394: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:55.402394: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:55.402394: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:55.403394:   - 设备句柄: 1495381146272
2025-08-26 09:25:55.403394:   - FetchRecords返回值: 0
2025-08-26 09:25:55.403394:   - 报告数量: 0
2025-08-26 09:25:55.404394: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:55.404394:   - 发现标签数量: 0
2025-08-26 09:25:55.404394:   - 未发现任何RFID标签
2025-08-26 09:25:55.405393: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:55.405393: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:55.566391: 接收到数据: aa 00 c8 80 00 00 27 82
2025-08-26 09:25:55.567391: 🔍 接收到串口数据: aa 00 c8 80 00 00 27 82
2025-08-26 09:25:55.567391: 🔍 数据长度: 8 字节
2025-08-26 09:25:55.567391: 🔍 预定义命令列表:
2025-08-26 09:25:55.568391:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 09:25:55.568391:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 09:25:55.568391:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 09:25:55.568391:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 09:25:55.569390:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 09:25:55.569390:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 09:25:55.569390:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 09:25:55.569390:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 09:25:55.569390:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 09:25:55.570390:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 09:25:55.570390: ✅ 解析到闸机命令: GateCommand.exitStart
2025-08-26 09:25:55.570390: 解析到闸机命令: exit_start (出馆开始)
2025-08-26 09:25:55.570390: 收到闸机命令: exit_start (出馆开始)
2025-08-26 09:25:55.570390: 🚪 收到出馆开始命令，等待出馆到位信号...
2025-08-26 09:25:55.571390: 闸机状态变更: GateState.idle -> GateState.exitStarted
2025-08-26 09:25:55.571390: 闸机状态更新: GateState.idle -> GateState.exitStarted
2025-08-26 09:25:55.571390: 📊 流程状态：出馆流程已开始，等待到位信号
2025-08-26 09:25:55.571390: [channel_1] 收到闸机事件: state_changed
2025-08-26 09:25:55.572390: 📨 收到GateCoordinator事件: state_changed
2025-08-26 09:25:55.572390: 闸机状态变更: GateState.exitStarted
2025-08-26 09:25:55.572390: 🎨 处理状态变更UI: exitStarted
2025-08-26 09:25:55.572390: 未处理的状态变更UI: exitStarted
2025-08-26 09:25:55.572390: [channel_1] 收到闸机事件: exit_start
2025-08-26 09:25:55.572390: [channel_1] 主从机扩展：处理出馆开始（请求-响应模式）
2025-08-26 09:25:55.573390: 扫描结果已清空
2025-08-26 09:25:55.573390: 🧹 [channel_1] 已清空RFID服务扫描结果（页面计数重置）
2025-08-26 09:25:55.573390: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 09:25:55.573390: 🧹 开始清空共享扫描池...
2025-08-26 09:25:55.573390: 📊 清空前状态: 大小=0, 内容=[]
2025-08-26 09:25:55.573390: 🔄 重置RFID去重集合...
2025-08-26 09:25:55.573390: 🔄 开始重置已处理条码集合...
2025-08-26 09:25:55.573390: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 09:25:55.573390: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 09:25:55.574390: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:25:55.574390: 📊 当前tagList状态: 0个标签
2025-08-26 09:25:55.574390: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:25:55.574390: 🔄 开始RFID轮询检查...
2025-08-26 09:25:55.574390: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:55.574390: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:55.574390: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 09:25:55.574390: 📡 RFID扫描状态（清空后）: isScanning=false
2025-08-26 09:25:55.574390: ⚠️ RFID未在扫描状态，尝试启动数据收集以恢复轮询...
2025-08-26 09:25:55.574390: 开始RFID数据收集...
2025-08-26 09:25:55.574390: 🔄 RFID数据收集已在进行中，重置防重复机制
2025-08-26 09:25:55.575390: ✅ 已处理条码列表已清空，轮询将重新发现标签
2025-08-26 09:25:55.575390: ✅ 共享扫描池已清空: 0 -> 0
2025-08-26 09:25:55.575390: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 09:25:55.575390: 清空RFID扫描缓冲区...
2025-08-26 09:25:55.575390: 跳过HWTagProvider清空，保持标签列表用于轮询
2025-08-26 09:25:55.575390: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 09:25:55.575390: 🔄 开始重置已处理条码集合...
2025-08-26 09:25:55.575390: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 09:25:55.575390: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 09:25:55.575390: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:25:55.576390: 📊 当前tagList状态: 0个标签
2025-08-26 09:25:55.576390: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:25:55.576390: 🔄 开始RFID轮询检查...
2025-08-26 09:25:55.576390: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:55.576390: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:55.576390: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 09:25:55.576390: 📨 收到GateCoordinator事件: exit_start
2025-08-26 09:25:55.576390: 页面状态变更: SilencePageState.waitingExit
2025-08-26 09:25:55.577390: RFID数据收集已启动
2025-08-26 09:25:55.577390: ✅ 已启动RFID数据收集（恢复轮询）
2025-08-26 09:25:55.577390: 🔄 开始重置已处理条码集合...
2025-08-26 09:25:55.577390: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 09:25:55.577390: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 09:25:55.577390: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:25:55.577390: 📊 当前tagList状态: 0个标签
2025-08-26 09:25:55.577390: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:25:55.577390: 🔄 开始RFID轮询检查...
2025-08-26 09:25:55.577390: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:55.577390: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:55.578390: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 09:25:55.578390: 🧹 [channel_1] 主机清空列表1和RFID缓冲区: 清除0个条码
2025-08-26 09:25:55.901388: 🔄 开始RFID轮询检查...
2025-08-26 09:25:55.902384: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:55.902384: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:55.903385: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:55.903385:   - 设备句柄: 1495381146272
2025-08-26 09:25:55.903385:   - FetchRecords返回值: 0
2025-08-26 09:25:55.903385:   - 报告数量: 0
2025-08-26 09:25:55.904385: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:55.904385:   - 发现标签数量: 0
2025-08-26 09:25:55.904385:   - 未发现任何RFID标签
2025-08-26 09:25:55.904385: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:55.904385: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:56.401375: 🔄 开始RFID轮询检查...
2025-08-26 09:25:56.401375: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:56.401375: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:56.402376: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:56.402376:   - 设备句柄: 1495381146272
2025-08-26 09:25:56.402376:   - FetchRecords返回值: 0
2025-08-26 09:25:56.402376:   - 报告数量: 0
2025-08-26 09:25:56.402376: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:56.403375:   - 发现标签数量: 0
2025-08-26 09:25:56.403375:   - 未发现任何RFID标签
2025-08-26 09:25:56.403375: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:56.403375: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:56.578373: 📊 [channel_1] 主机返回当前数据: 0个条码
2025-08-26 09:25:56.900366: 🔄 开始RFID轮询检查...
2025-08-26 09:25:56.901367: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:56.901367: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:56.901367: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:56.901367:   - 设备句柄: 1495381146272
2025-08-26 09:25:56.902368:   - FetchRecords返回值: 0
2025-08-26 09:25:56.902368:   - 报告数量: 0
2025-08-26 09:25:56.902368: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:56.902368:   - 发现标签数量: 0
2025-08-26 09:25:56.903367:   - 未发现任何RFID标签
2025-08-26 09:25:56.903367: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:56.904367: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:57.126365: 接收到数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 09:25:57.127364: 🔍 接收到串口数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 09:25:57.127364: 🔍 数据长度: 8 字节
2025-08-26 09:25:57.127364: 🔍 预定义命令列表:
2025-08-26 09:25:57.128363:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 09:25:57.128363:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 09:25:57.128363:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 09:25:57.128363:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 09:25:57.129363:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 09:25:57.129363:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 09:25:57.129363:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 09:25:57.129363:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 09:25:57.129363:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 09:25:57.130363:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 09:25:57.130363: ✅ 解析到闸机命令: GateCommand.reachPosition
2025-08-26 09:25:57.130363: 解析到闸机命令: position_reached (到达指定位置)
2025-08-26 09:25:57.131364: 收到闸机命令: position_reached (到达指定位置)
2025-08-26 09:25:57.132363: 📍 收到到位信号，当前状态: GateState.exitStarted
2025-08-26 09:25:57.132363: 📊 流程状态：进馆=false, 出馆=true
2025-08-26 09:25:57.132363: 📊 待处理认证：进馆=false, 出馆=false
2025-08-26 09:25:57.132363: 🚪 出馆到位信号，启动认证和10秒数据收集...
2025-08-26 09:25:57.132363: 闸机状态变更: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 09:25:57.132363: 闸机状态更新: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 09:25:57.133363: 🔐 启动出馆认证系统（不关注结果）...
2025-08-26 09:25:57.133363: 闸机状态变更: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 09:25:57.133363: 闸机状态更新: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 09:25:57.133363: 多认证管理器状态变更: listening
2025-08-26 09:25:57.134364: 启动所有认证方式监听: [AuthMethod.readerCard]
2025-08-26 09:25:57.134364: 准备启动 1 个物理认证服务
2025-08-26 09:25:57.134364: 开始读卡器认证监听
2025-08-26 09:25:57.134364: 🔥 测试：跳过强制重新配置，保持现有连接
2025-08-26 09:25:57.134364: 已移除读卡器状态监听器
2025-08-26 09:25:57.134364: 已移除标签数据监听器
2025-08-26 09:25:57.135363: 所有卡片监听器已移除
2025-08-26 09:25:57.135363: 已添加读卡器状态监听器
2025-08-26 09:25:57.135363: 已添加标签数据监听器
2025-08-26 09:25:57.135363: 开始监听卡片数据 - 所有监听器已就绪
2025-08-26 09:25:57.135363: 读卡器认证监听启动成功
2025-08-26 09:25:57.135363: ✅ 出馆认证系统已启动
2025-08-26 09:25:57.136363: 🚀 启动出馆10秒数据收集...
2025-08-26 09:25:57.136363: 🔧 启动10秒计时器，当前时间: 2025-08-26 09:25:57.131364
2025-08-26 09:25:57.136363: 🔧 10秒计时器已设置，计时器对象: Instance of '_Timer'
2025-08-26 09:25:57.136363: 📡 开始从共享池收集数据...
2025-08-26 09:25:57.136363: 🔧 RFID扫描已在运行，只清空共享池准备收集新数据
2025-08-26 09:25:57.136363: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 09:25:57.136363: 🧹 开始清空共享扫描池...
2025-08-26 09:25:57.136363: 📊 清空前状态: 大小=0, 内容=[]
2025-08-26 09:25:57.136363: 🔄 重置RFID去重集合...
2025-08-26 09:25:57.136363: 🔄 开始重置已处理条码集合...
2025-08-26 09:25:57.136363: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 09:25:57.137363: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 09:25:57.137363: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:25:57.137363: 📊 当前tagList状态: 0个标签
2025-08-26 09:25:57.137363: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:25:57.137363: 🔄 开始RFID轮询检查...
2025-08-26 09:25:57.137363: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:57.137363: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:57.137363: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 09:25:57.137363: 📡 RFID扫描状态（清空后）: isScanning=true
2025-08-26 09:25:57.137363: ✅ 共享扫描池已清空: 0 -> 0
2025-08-26 09:25:57.138363: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 09:25:57.138363: 清空RFID扫描缓冲区...
2025-08-26 09:25:57.138363: 跳过HWTagProvider清空，保持标签列表用于轮询
2025-08-26 09:25:57.138363: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 09:25:57.138363: 🔄 开始重置已处理条码集合...
2025-08-26 09:25:57.138363: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 09:25:57.138363: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 09:25:57.138363: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:25:57.138363: 📊 当前tagList状态: 0个标签
2025-08-26 09:25:57.138363: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:25:57.138363: 🔄 开始RFID轮询检查...
2025-08-26 09:25:57.139363: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:57.139363: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:57.139363: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 09:25:57.139363: [channel_1] 收到闸机事件: state_changed
2025-08-26 09:25:57.139363: 📨 收到GateCoordinator事件: state_changed
2025-08-26 09:25:57.139363: 闸机状态变更: GateState.exitWaitingAuth
2025-08-26 09:25:57.139363: 🎨 处理状态变更UI: exitWaitingAuth
2025-08-26 09:25:57.140363: 未处理的状态变更UI: exitWaitingAuth
2025-08-26 09:25:57.140363: 读者证 认证服务启动成功
2025-08-26 09:25:57.140363: 所有认证服务启动完成，成功启动 1 个服务
2025-08-26 09:25:57.140363: 当前可用的认证方式: 读者证
2025-08-26 09:25:57.140363: 🔄 开始重置已处理条码集合...
2025-08-26 09:25:57.140363: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 09:25:57.140363: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 09:25:57.141362: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:25:57.141362: 📊 当前tagList状态: 0个标签
2025-08-26 09:25:57.141362: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:25:57.141362: 🔄 开始RFID轮询检查...
2025-08-26 09:25:57.141362: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:57.141362: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:57.141362: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 09:25:57.142362: [channel_1] 收到闸机事件: state_changed
2025-08-26 09:25:57.142362: 📨 收到GateCoordinator事件: state_changed
2025-08-26 09:25:57.142362: 闸机状态变更: GateState.exitScanning
2025-08-26 09:25:57.142362: 🎨 处理状态变更UI: exitScanning
2025-08-26 09:25:57.142362: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 09:25:57.143363: 🧪 模拟模式：检测到测试卡片（调用次数: 1）
2025-08-26 09:25:57.143363: 🧪 模拟模式 - 使用测试patron: 2017621493 (索引: 0, 总调用次数: 1)
2025-08-26 09:25:57.143363: 🧪 模拟检测到卡片: 2017621493
2025-08-26 09:25:57.143363: 读卡器数据认证：
2025-08-26 09:25:57.143363:   设备类型: 10
2025-08-26 09:25:57.144362:   条码: 2017621493
2025-08-26 09:25:57.144362:   标签UID: SIM2017621493
2025-08-26 09:25:57.144362:   对应登录类型: AuthLoginType.readerCard
2025-08-26 09:25:57.144362:   根据读卡器类型10确定认证方式为: 读者证
2025-08-26 09:25:57.144362:   开始调用认证API: 2017621493
2025-08-26 09:25:57.144362: 正在认证用户: 2017621493, 方式: 读者证
2025-08-26 09:25:57.144362: 多认证管理器: 读者证获得认证请求锁
2025-08-26 09:25:57.144362: 使用门径认证接口: identifier=2017621493, method=AuthMethod.readerCard, isEnter=false
2025-08-26 09:25:57.145362: 开始门径认证: identifier=2017621493, method=AuthMethod.readerCard, isEnter=false
2025-08-26 09:25:57.145362: 发送认证请求: {deviceMac: FFFFFFFF, patronSn: 2017621493, cardSn: null, type: 2}
2025-08-26 09:25:57.145362: 设备配置文件不存在，使用默认配置: C:\Users\<USER>\Desktop\Release\Release\device_config.json
2025-08-26 09:25:57.145362: 设备API服务初始化完成: http://166.111.120.166:9000/tunano/ldc/entrance
2025-08-26 09:25:57.145362: 设备配置文件不存在，使用默认配置: C:\Users\<USER>\Desktop\Release\Release\device_config.json
2025-08-26 09:25:57.146362: 发送读者认证请求: /v1/api/door/verify
2025-08-26 09:25:57.146362: 请求数据: {"deviceMac":"FFFFFFFF","patronSn":"2017621493","cardSn":null,"type":2}
2025-08-26 09:25:57.169362: 读者认证响应: {errorCode: 0, message: 验证通过, data: null}
2025-08-26 09:25:57.170362: 认证响应: errorCode=0, message=验证通过
2025-08-26 09:25:57.170362: 认证成功，跳过用户信息获取
2025-08-26 09:25:57.170362: 门径认证结果: AuthStatus.success, 用户=认证用户
2025-08-26 09:25:57.170362:   认证结果: AuthStatus.success, 方式: 读者证
2025-08-26 09:25:57.170362: 认证结果已产生，立即停止读卡器扫描
2025-08-26 09:25:57.171362: 没有活跃的读卡器连接需要停止
2025-08-26 09:25:57.171362: 多认证管理器: 收到认证结果，立即停止所有读卡器扫描
2025-08-26 09:25:57.171362: 立即停止所有读卡器扫描
2025-08-26 09:25:57.171362: 停止读卡器认证监听
2025-08-26 09:25:57.171362: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-26 09:25:57.171362: 多认证管理器状态变更: authenticating
2025-08-26 09:25:57.171362: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-26 09:25:57.171362: 多认证管理器状态变更: completed
2025-08-26 09:25:57.172363: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-26 09:25:57.172363: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-26 09:25:57.172363: 已移除读卡器状态监听器
2025-08-26 09:25:57.173364: 已移除标签数据监听器
2025-08-26 09:25:57.173364: 所有卡片监听器已移除
2025-08-26 09:25:57.173364: 没有活跃的读卡器连接需要暂停
2025-08-26 09:25:57.173364: 📱 收到出馆认证结果: AuthStatus.success, 用户: 认证用户
2025-08-26 09:25:57.174365: ✅ 出馆认证API请求已发送，继续数据收集流程（不关注认证结果）
2025-08-26 09:25:57.174365: 读卡器认证监听已停止（连接保持）
2025-08-26 09:25:57.174365: 已停止 读者证 扫描
2025-08-26 09:25:57.174365: [channel_1] 收到闸机事件: exit_auth_success
2025-08-26 09:25:57.175365: 📨 收到GateCoordinator事件: exit_auth_success
2025-08-26 09:25:57.175365: 未处理的GateCoordinator事件: exit_auth_success
2025-08-26 09:25:57.401358: 🔄 开始RFID轮询检查...
2025-08-26 09:25:57.401358: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:57.401358: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:57.402358: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:57.402358:   - 设备句柄: 1495381146272
2025-08-26 09:25:57.402358:   - FetchRecords返回值: 0
2025-08-26 09:25:57.402358:   - 报告数量: 0
2025-08-26 09:25:57.403358: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:57.403358:   - 发现标签数量: 0
2025-08-26 09:25:57.403358:   - 未发现任何RFID标签
2025-08-26 09:25:57.403358: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:57.403358: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:57.900349: 🔄 开始RFID轮询检查...
2025-08-26 09:25:57.900349: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:57.900349: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:57.902349: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:57.902349:   - 设备句柄: 1495381146272
2025-08-26 09:25:57.902349:   - FetchRecords返回值: 0
2025-08-26 09:25:57.902349:   - 报告数量: 0
2025-08-26 09:25:57.902349: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:57.903349:   - 发现标签数量: 0
2025-08-26 09:25:57.903349:   - 未发现任何RFID标签
2025-08-26 09:25:57.903349: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:57.903349: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:58.401341: 🔄 开始RFID轮询检查...
2025-08-26 09:25:58.401341: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:58.401341: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:58.402342: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:58.402342:   - 设备句柄: 1495381146272
2025-08-26 09:25:58.403345:   - FetchRecords返回值: 0
2025-08-26 09:25:58.403345:   - 报告数量: 0
2025-08-26 09:25:58.403345: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:58.403345:   - 发现标签数量: 0
2025-08-26 09:25:58.403345:   - 未发现任何RFID标签
2025-08-26 09:25:58.404342: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:58.404342: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:58.900332: 🔄 开始RFID轮询检查...
2025-08-26 09:25:58.900332: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:58.900332: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:58.902332: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:58.902332:   - 设备句柄: 1495381146272
2025-08-26 09:25:58.902332:   - FetchRecords返回值: 0
2025-08-26 09:25:58.902332:   - 报告数量: 0
2025-08-26 09:25:58.903332: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:58.903332:   - 发现标签数量: 0
2025-08-26 09:25:58.903332:   - 未发现任何RFID标签
2025-08-26 09:25:58.903332: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:58.903332: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:59.400323: 🔄 开始RFID轮询检查...
2025-08-26 09:25:59.400323: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:59.400323: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:59.402323: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:59.402323:   - 设备句柄: 1495381146272
2025-08-26 09:25:59.402323:   - FetchRecords返回值: 0
2025-08-26 09:25:59.403324:   - 报告数量: 0
2025-08-26 09:25:59.403324: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:59.403324:   - 发现标签数量: 0
2025-08-26 09:25:59.403324:   - 未发现任何RFID标签
2025-08-26 09:25:59.404324: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:59.404324: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:25:59.900317: 🔄 开始RFID轮询检查...
2025-08-26 09:25:59.900317: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:25:59.901315: ⚠️ tagList为空，场上无标签
2025-08-26 09:25:59.901315: 🔍 LSGate硬件扫描详情:
2025-08-26 09:25:59.901315:   - 设备句柄: 1495381146272
2025-08-26 09:25:59.901315:   - FetchRecords返回值: 0
2025-08-26 09:25:59.901315:   - 报告数量: 0
2025-08-26 09:25:59.902315: 📊 LSGate扫描结果汇总:
2025-08-26 09:25:59.902315:   - 发现标签数量: 0
2025-08-26 09:25:59.902315:   - 未发现任何RFID标签
2025-08-26 09:25:59.902315: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:25:59.903315: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:26:00.400306: 🔄 开始RFID轮询检查...
2025-08-26 09:26:00.400306: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:26:00.400306: ⚠️ tagList为空，场上无标签
2025-08-26 09:26:00.402306: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:00.402306:   - 设备句柄: 1495381146272
2025-08-26 09:26:00.402306:   - FetchRecords返回值: 0
2025-08-26 09:26:00.402306:   - 报告数量: 0
2025-08-26 09:26:00.402306: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:00.402306:   - 发现标签数量: 0
2025-08-26 09:26:00.403306:   - 未发现任何RFID标签
2025-08-26 09:26:00.403306: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 09:26:00.403306: 🚫 LSGate未检测到任何RFID标签
2025-08-26 09:26:00.900297: 🔄 开始RFID轮询检查...
2025-08-26 09:26:00.901297: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 09:26:00.901297: ⚠️ tagList为空，场上无标签
2025-08-26 09:26:00.901297: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:00.902298:   - 设备句柄: 1495381146272
2025-08-26 09:26:00.902298:   - FetchRecords返回值: 0
2025-08-26 09:26:00.902298:   - 报告数量: 1
2025-08-26 09:26:00.903297:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:00.903297:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:00.903297:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:00.903297:   - 设备类型: LSGControlCenter
2025-08-26 09:26:00.903297:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:00.903297:   - 数据长度: 8
2025-08-26 09:26:00.904297:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:00.904297:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:00.904297: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:00.904297:   - 发现标签数量: 1
2025-08-26 09:26:00.905297:   - 标签详情:
2025-08-26 09:26:00.905297:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:00.905297: RFID扫描: 发现 1 个标签
2025-08-26 09:26:00.905297: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 09:26:00.905297: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:00.905297: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:00.906297:   - UID: E004015305F83C8E
2025-08-26 09:26:00.906297:   - Data: E004015305F83C8E
2025-08-26 09:26:00.906297:   - EventType: 1
2025-08-26 09:26:00.906297:   - Direction: 0
2025-08-26 09:26:00.906297:   - Antenna: 1
2025-08-26 09:26:00.906297:   - TagFrequency: 0
2025-08-26 09:26:00.906297: data:E004015305F83C8E,coder:Coder15962Std
2025-08-26 09:26:00.907297: parseRet：{"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]},decoder:15962标准协议
2025-08-26 09:26:00.907297: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-26 09:26:00.907297: 🏷️ 新标签[0]: uid=E004015305F83C8E, barCode=null, readerType=22
2025-08-26 09:26:00.907297: 📡 LSGate标签详情: {uid: E004015305F83C8E, barCode: null, eas: null, tagType: null, libraryCode: null, afiStr: null, afiData: null, readerType: 22, decoderType: 15962标准协议, dsfID: null, oidList: [{oid: 0, compressMode: 110, data: S, originHexStr: E004015300000000, originData: [53], isKeepInTag: true}], data: E004015305F83C8E, leftBinary: null, sortType: null, codeType: null, version: null, contentIndex: null, tagFrequency: 0, info: null, inAnt: 1}
2025-08-26 09:26:00.907297: 🎯 HWTagProvider: 当前总标签数 1, 新增标签数 1
2025-08-26 09:26:01.401289: 🔄 开始RFID轮询检查...
2025-08-26 09:26:01.402290: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=0个
2025-08-26 09:26:01.402290: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:01.402290: 🆕 轮询发现新标签(UID作为条码): E004015305F83C8E
2025-08-26 09:26:01.402290: 📋 添加到已处理列表: 0 -> 1
2025-08-26 09:26:01.402290: 🏷️ 检测到标签: E004015305F83C8E
2025-08-26 09:26:01.402290: 📊 当前扫描状态: 扫描中=true, 已扫描=1个
2025-08-26 09:26:01.403289: 📋 已扫描列表: [E004015305F83C8E]
2025-08-26 09:26:01.403289: ✅ 条码已发送到barcodeStream，将进入共享池: E004015305F83C8E
2025-08-26 09:26:01.403289: 📡 barcodeStream监听器数量: 有监听器
2025-08-26 09:26:01.403289: ✅ 轮询完成: 发现1个新标签，总计已处理1个标签
2025-08-26 09:26:01.403289: 📋 当前已处理标签列表: [E004015305F83C8E]
2025-08-26 09:26:01.403289: 🔄 尝试添加条码到共享池: E004015305F83C8E
2025-08-26 09:26:01.404289: 📊 添加前状态: 共享池大小=0, 是否为空=true
2025-08-26 09:26:01.404289: ✅ 成功添加条码到共享池: E004015305F83C8E (总计: 1)
2025-08-26 09:26:01.404289: 📋 当前共享池内容: [E004015305F83C8E]
2025-08-26 09:26:01.404289: 📡 共享池变化通知已发送
2025-08-26 09:26:01.404289: 🔄 尝试添加条码到共享池: E004015305F83C8E
2025-08-26 09:26:01.404289: 📊 添加前状态: 共享池大小=1, 是否为空=false
2025-08-26 09:26:01.404289: 🔄 条码已存在于共享池: E004015305F83C8E (总计: 1)
2025-08-26 09:26:01.404289: 扫描到新条码: E004015305F83C8E (总计: 1)
2025-08-26 09:26:01.404289: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 09:26:01.405288: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:01.405288:   - 设备句柄: 1495381146272
2025-08-26 09:26:01.405288:   - FetchRecords返回值: 0
2025-08-26 09:26:01.405288:   - 报告数量: 1
2025-08-26 09:26:01.405288:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:01.406289:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:01.406289:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:01.406289:   - 设备类型: LSGControlCenter
2025-08-26 09:26:01.406289:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:01.406289:   - 数据长度: 8
2025-08-26 09:26:01.406289:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:01.406289:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:01.407288: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:01.407288:   - 发现标签数量: 1
2025-08-26 09:26:01.407288:   - 标签详情:
2025-08-26 09:26:01.407288:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:01.407288: RFID扫描: 发现 1 个标签
2025-08-26 09:26:01.407288: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 09:26:01.408306: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:01.408306: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:01.409294:   - UID: E004015305F83C8E
2025-08-26 09:26:01.409294:   - Data: E004015305F83C8E
2025-08-26 09:26:01.409294:   - EventType: 1
2025-08-26 09:26:01.410289:   - Direction: 0
2025-08-26 09:26:01.410289:   - Antenna: 1
2025-08-26 09:26:01.410289:   - TagFrequency: 0
2025-08-26 09:26:01.901279: 🔄 开始RFID轮询检查...
2025-08-26 09:26:01.901279: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:01.901279: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:01.901279: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:01.902280: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:01.902280: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:01.902280:   - 设备句柄: 1495381146272
2025-08-26 09:26:01.903279:   - FetchRecords返回值: 0
2025-08-26 09:26:01.903279:   - 报告数量: 1
2025-08-26 09:26:01.903279:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:01.903279:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:01.903279:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:01.903279:   - 设备类型: LSGControlCenter
2025-08-26 09:26:01.903279:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:01.904280:   - 数据长度: 8
2025-08-26 09:26:01.904280:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:01.904280:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:01.904280: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:01.904280:   - 发现标签数量: 1
2025-08-26 09:26:01.904280:   - 标签详情:
2025-08-26 09:26:01.904280:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:01.904280: RFID扫描: 发现 1 个标签
2025-08-26 09:26:01.905279: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 09:26:01.905279: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:01.905279: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:01.905279:   - UID: E004015305F83C8E
2025-08-26 09:26:01.905279:   - Data: E004015305F83C8E
2025-08-26 09:26:01.905279:   - EventType: 1
2025-08-26 09:26:01.905279:   - Direction: 0
2025-08-26 09:26:01.905279:   - Antenna: 1
2025-08-26 09:26:01.906280:   - TagFrequency: 0
2025-08-26 09:26:02.173275: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-26 09:26:02.401272: 🔄 开始RFID轮询检查...
2025-08-26 09:26:02.402272: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:02.402272: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:02.402272: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:02.402272: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:02.403272: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:02.403272:   - 设备句柄: 1495381146272
2025-08-26 09:26:02.403272:   - FetchRecords返回值: 0
2025-08-26 09:26:02.404271:   - 报告数量: 1
2025-08-26 09:26:02.404271:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:02.404271:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:02.404271:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:02.404271:   - 设备类型: LSGControlCenter
2025-08-26 09:26:02.405271:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:02.405271:   - 数据长度: 8
2025-08-26 09:26:02.405271:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:02.405271:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:02.405271: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:02.405271:   - 发现标签数量: 1
2025-08-26 09:26:02.406271:   - 标签详情:
2025-08-26 09:26:02.406271:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:02.406271: RFID扫描: 发现 1 个标签
2025-08-26 09:26:02.406271: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 09:26:02.406271: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:02.406271: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:02.406271:   - UID: E004015305F83C8E
2025-08-26 09:26:02.406271:   - Data: E004015305F83C8E
2025-08-26 09:26:02.407271:   - EventType: 1
2025-08-26 09:26:02.407271:   - Direction: 0
2025-08-26 09:26:02.407271:   - Antenna: 1
2025-08-26 09:26:02.407271:   - TagFrequency: 0
2025-08-26 09:26:02.901263: 🔄 开始RFID轮询检查...
2025-08-26 09:26:02.901263: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:02.901263: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:02.901263: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:02.902262: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:02.902262: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:02.903263:   - 设备句柄: 1495381146272
2025-08-26 09:26:02.903263:   - FetchRecords返回值: 0
2025-08-26 09:26:02.903263:   - 报告数量: 1
2025-08-26 09:26:02.903263:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:02.903263:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:02.903263:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:02.904262:   - 设备类型: LSGControlCenter
2025-08-26 09:26:02.904262:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:02.904262:   - 数据长度: 8
2025-08-26 09:26:02.904262:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:02.904262:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:02.904262: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:02.904262:   - 发现标签数量: 1
2025-08-26 09:26:02.905262:   - 标签详情:
2025-08-26 09:26:02.905262:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:02.905262: RFID扫描: 发现 1 个标签
2025-08-26 09:26:02.905262: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 09:26:02.905262: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:02.905262: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:02.905262:   - UID: E004015305F83C8E
2025-08-26 09:26:02.905262:   - Data: E004015305F83C8E
2025-08-26 09:26:02.906262:   - EventType: 1
2025-08-26 09:26:02.906262:   - Direction: 0
2025-08-26 09:26:02.906262:   - Antenna: 1
2025-08-26 09:26:02.906262:   - TagFrequency: 0
2025-08-26 09:26:03.401253: 🔄 开始RFID轮询检查...
2025-08-26 09:26:03.401253: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:03.401253: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:03.401253: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:03.402254: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:03.403253: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:03.404257:   - 设备句柄: 1495381146272
2025-08-26 09:26:03.404257:   - FetchRecords返回值: 0
2025-08-26 09:26:03.405257:   - 报告数量: 2
2025-08-26 09:26:03.405257:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:03.405257:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:03.406256:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:03.406256:   - 设备类型: LSGControlCenter
2025-08-26 09:26:03.406256:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:03.407255:   - 数据长度: 8
2025-08-26 09:26:03.407255:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:03.407255:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:03.408257:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:03.408257:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:03.408257:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:03.408257:   - 设备类型: LSGControlCenter
2025-08-26 09:26:03.409254:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:03.409254:   - 数据长度: 8
2025-08-26 09:26:03.409254:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:03.409254:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:03.409254: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:03.409254:   - 发现标签数量: 2
2025-08-26 09:26:03.410254:   - 标签详情:
2025-08-26 09:26:03.410254:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:03.410254:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:03.410254: RFID扫描: 发现 2 个标签
2025-08-26 09:26:03.410254: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:03.411254: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:03.411254: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:03.411254:   - UID: E004015305F83C8E
2025-08-26 09:26:03.411254:   - Data: E004015305F83C8E
2025-08-26 09:26:03.412254:   - EventType: 1
2025-08-26 09:26:03.412254:   - Direction: 0
2025-08-26 09:26:03.412254:   - Antenna: 1
2025-08-26 09:26:03.412254:   - TagFrequency: 0
2025-08-26 09:26:03.413254: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:03.413254:   - UID: E004015305F83C8E
2025-08-26 09:26:03.413254:   - Data: E004015305F83C8E
2025-08-26 09:26:03.413254:   - EventType: 1
2025-08-26 09:26:03.413254:   - Direction: 0
2025-08-26 09:26:03.413254:   - Antenna: 1
2025-08-26 09:26:03.414253:   - TagFrequency: 0
2025-08-26 09:26:03.900245: 🔄 开始RFID轮询检查...
2025-08-26 09:26:03.900245: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:03.901245: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:03.901245: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:03.901245: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:03.902245: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:03.902245:   - 设备句柄: 1495381146272
2025-08-26 09:26:03.902245:   - FetchRecords返回值: 0
2025-08-26 09:26:03.902245:   - 报告数量: 2
2025-08-26 09:26:03.902245:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:03.903245:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:03.903245:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:03.903245:   - 设备类型: LSGControlCenter
2025-08-26 09:26:03.903245:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:03.903245:   - 数据长度: 8
2025-08-26 09:26:03.903245:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:03.903245:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:03.904245:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:03.904245:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:03.904245:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:03.904245:   - 设备类型: LSGControlCenter
2025-08-26 09:26:03.904245:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:03.904245:   - 数据长度: 8
2025-08-26 09:26:03.904245:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:03.904245:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:03.905244: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:03.905244:   - 发现标签数量: 2
2025-08-26 09:26:03.905244:   - 标签详情:
2025-08-26 09:26:03.905244:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:03.905244:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:03.905244: RFID扫描: 发现 2 个标签
2025-08-26 09:26:03.905244: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:03.905244: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:03.906244: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:03.906244:   - UID: E004015305F83C8E
2025-08-26 09:26:03.906244:   - Data: E004015305F83C8E
2025-08-26 09:26:03.906244:   - EventType: 1
2025-08-26 09:26:03.906244:   - Direction: 0
2025-08-26 09:26:03.906244:   - Antenna: 1
2025-08-26 09:26:03.906244:   - TagFrequency: 0
2025-08-26 09:26:03.906244: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:03.907244:   - UID: E004015305F83C8E
2025-08-26 09:26:03.907244:   - Data: E004015305F83C8E
2025-08-26 09:26:03.907244:   - EventType: 1
2025-08-26 09:26:03.907244:   - Direction: 0
2025-08-26 09:26:03.907244:   - Antenna: 1
2025-08-26 09:26:03.907244:   - TagFrequency: 0
2025-08-26 09:26:04.401236: 🔄 开始RFID轮询检查...
2025-08-26 09:26:04.401236: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:04.401236: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:04.401236: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:04.402237: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:04.403236: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:04.403236:   - 设备句柄: 1495381146272
2025-08-26 09:26:04.403236:   - FetchRecords返回值: 0
2025-08-26 09:26:04.403236:   - 报告数量: 2
2025-08-26 09:26:04.403236:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:04.404236:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:04.404236:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:04.404236:   - 设备类型: LSGControlCenter
2025-08-26 09:26:04.404236:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:04.404236:   - 数据长度: 8
2025-08-26 09:26:04.404236:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:04.404236:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:04.405236:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:04.405236:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:04.405236:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:04.405236:   - 设备类型: LSGControlCenter
2025-08-26 09:26:04.405236:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:04.405236:   - 数据长度: 8
2025-08-26 09:26:04.405236:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:04.406236:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:04.406236: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:04.406236:   - 发现标签数量: 2
2025-08-26 09:26:04.406236:   - 标签详情:
2025-08-26 09:26:04.406236:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:04.406236:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:04.406236: RFID扫描: 发现 2 个标签
2025-08-26 09:26:04.406236: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:04.407236: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:04.407236: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:04.407236:   - UID: E004015305F83C8E
2025-08-26 09:26:04.407236:   - Data: E004015305F83C8E
2025-08-26 09:26:04.407236:   - EventType: 1
2025-08-26 09:26:04.407236:   - Direction: 0
2025-08-26 09:26:04.407236:   - Antenna: 1
2025-08-26 09:26:04.407236:   - TagFrequency: 0
2025-08-26 09:26:04.408236: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:04.408236:   - UID: E004015305F83C8E
2025-08-26 09:26:04.408236:   - Data: E004015305F83C8E
2025-08-26 09:26:04.408236:   - EventType: 1
2025-08-26 09:26:04.408236:   - Direction: 0
2025-08-26 09:26:04.408236:   - Antenna: 1
2025-08-26 09:26:04.409247:   - TagFrequency: 0
2025-08-26 09:26:04.901228: 🔄 开始RFID轮询检查...
2025-08-26 09:26:04.901228: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:04.901228: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:04.902228: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:04.902228: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:04.903228: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:04.903228:   - 设备句柄: 1495381146272
2025-08-26 09:26:04.903228:   - FetchRecords返回值: 0
2025-08-26 09:26:04.904227:   - 报告数量: 2
2025-08-26 09:26:04.904227:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:04.904227:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:04.904227:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:04.905230:   - 设备类型: LSGControlCenter
2025-08-26 09:26:04.905230:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:04.905230:   - 数据长度: 8
2025-08-26 09:26:04.905230:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:04.905230:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:04.906227:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:04.906227:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:04.906227:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:04.906227:   - 设备类型: LSGControlCenter
2025-08-26 09:26:04.906227:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:04.907228:   - 数据长度: 8
2025-08-26 09:26:04.907228:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:04.907228:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:04.907228: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:04.908228:   - 发现标签数量: 2
2025-08-26 09:26:04.908228:   - 标签详情:
2025-08-26 09:26:04.908228:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:04.908228:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:04.909229: RFID扫描: 发现 2 个标签
2025-08-26 09:26:04.909229: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:04.909229: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:04.909229: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:04.909229:   - UID: E004015305F83C8E
2025-08-26 09:26:04.909229:   - Data: E004015305F83C8E
2025-08-26 09:26:04.909229:   - EventType: 1
2025-08-26 09:26:04.910228:   - Direction: 0
2025-08-26 09:26:04.910228:   - Antenna: 1
2025-08-26 09:26:04.910228:   - TagFrequency: 0
2025-08-26 09:26:04.910228: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:04.910228:   - UID: E004015305F83C8E
2025-08-26 09:26:04.910228:   - Data: E004015305F83C8E
2025-08-26 09:26:04.910228:   - EventType: 1
2025-08-26 09:26:04.911227:   - Direction: 0
2025-08-26 09:26:04.911227:   - Antenna: 1
2025-08-26 09:26:04.911227:   - TagFrequency: 0
2025-08-26 09:26:05.173222: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-26 09:26:05.173222: 多认证管理器状态变更: listening
2025-08-26 09:26:05.174223: ⚠️ 人脸识别服务未初始化或不可用
2025-08-26 09:26:05.326220: 接收到数据: aa 00 c9 80 00 00 26 7e
2025-08-26 09:26:05.327220: 🔍 接收到串口数据: aa 00 c9 80 00 00 26 7e
2025-08-26 09:26:05.327220: 🔍 数据长度: 8 字节
2025-08-26 09:26:05.327220: 🔍 预定义命令列表:
2025-08-26 09:26:05.327220:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 09:26:05.328220:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 09:26:05.328220:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 09:26:05.328220:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 09:26:05.328220:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 09:26:05.328220:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 09:26:05.328220:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 09:26:05.329220:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 09:26:05.329220:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 09:26:05.329220:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 09:26:05.329220: ✅ 解析到闸机命令: GateCommand.exitEnd
2025-08-26 09:26:05.329220: 解析到闸机命令: exit_end (出馆结束)
2025-08-26 09:26:05.329220: 收到闸机命令: exit_end (出馆结束)
2025-08-26 09:26:05.330222: 出馆流程结束
2025-08-26 09:26:05.330222: 闸机状态变更: GateState.exitScanning -> GateState.idle
2025-08-26 09:26:05.330222: 闸机状态更新: GateState.exitScanning -> GateState.idle
2025-08-26 09:26:05.330222: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-26 09:26:05.331223: [channel_1] 收到闸机事件: state_changed
2025-08-26 09:26:05.331223: 📨 收到GateCoordinator事件: state_changed
2025-08-26 09:26:05.331223: 闸机状态变更: GateState.idle
2025-08-26 09:26:05.331223: 🎨 处理状态变更UI: idle
2025-08-26 09:26:05.331223: 页面状态变更: SilencePageState.welcome
2025-08-26 09:26:05.331223: [channel_1] 收到闸机事件: exit_end
2025-08-26 09:26:05.332220: [channel_1] 主从机扩展：处理出馆结束
2025-08-26 09:26:05.332220: [channel_1] 清空处理队列，当前大小: 0
2025-08-26 09:26:05.332220: [channel_1] 处理队列已清空
2025-08-26 09:26:05.332220: 📨 收到GateCoordinator事件: exit_end
2025-08-26 09:26:05.332220: 页面状态变更: SilencePageState.welcome
2025-08-26 09:26:05.332220: [channel_1] 通知收集到的条码: []
2025-08-26 09:26:05.332220: ✅ [channel_1] 数据流通知发送成功: 0个条码
2025-08-26 09:26:05.401218: 🔄 开始RFID轮询检查...
2025-08-26 09:26:05.401218: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:05.401218: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:05.401218: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:05.401218: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:05.403218: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:05.403218:   - 设备句柄: 1495381146272
2025-08-26 09:26:05.403218:   - FetchRecords返回值: 0
2025-08-26 09:26:05.403218:   - 报告数量: 2
2025-08-26 09:26:05.404218:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:05.404218:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:05.404218:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:05.404218:   - 设备类型: LSGControlCenter
2025-08-26 09:26:05.404218:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:05.404218:   - 数据长度: 8
2025-08-26 09:26:05.404218:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:05.405218:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:05.405218:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:05.405218:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:05.405218:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:05.405218:   - 设备类型: LSGControlCenter
2025-08-26 09:26:05.405218:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:05.405218:   - 数据长度: 8
2025-08-26 09:26:05.405218:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:05.406218:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:05.406218: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:05.406218:   - 发现标签数量: 2
2025-08-26 09:26:05.406218:   - 标签详情:
2025-08-26 09:26:05.406218:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:05.406218:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:05.406218: RFID扫描: 发现 2 个标签
2025-08-26 09:26:05.406218: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:05.407218: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:05.407218: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:05.407218:   - UID: E004015305F83C8E
2025-08-26 09:26:05.407218:   - Data: E004015305F83C8E
2025-08-26 09:26:05.407218:   - EventType: 1
2025-08-26 09:26:05.407218:   - Direction: 0
2025-08-26 09:26:05.407218:   - Antenna: 1
2025-08-26 09:26:05.407218:   - TagFrequency: 0
2025-08-26 09:26:05.408219: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:05.408219:   - UID: E004015305F83C8E
2025-08-26 09:26:05.408219:   - Data: E004015305F83C8E
2025-08-26 09:26:05.408219:   - EventType: 1
2025-08-26 09:26:05.408219:   - Direction: 0
2025-08-26 09:26:05.408219:   - Antenna: 1
2025-08-26 09:26:05.408219:   - TagFrequency: 0
2025-08-26 09:26:05.900210: 🔄 开始RFID轮询检查...
2025-08-26 09:26:05.900210: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:05.900210: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:05.900210: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:05.900210: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:05.903210: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:05.903210:   - 设备句柄: 1495381146272
2025-08-26 09:26:05.903210:   - FetchRecords返回值: 0
2025-08-26 09:26:05.903210:   - 报告数量: 2
2025-08-26 09:26:05.903210:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:05.904210:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:05.904210:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:05.904210:   - 设备类型: LSGControlCenter
2025-08-26 09:26:05.904210:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:05.904210:   - 数据长度: 8
2025-08-26 09:26:05.904210:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:05.904210:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:05.904210:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:05.905210:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:05.905210:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:05.905210:   - 设备类型: LSGControlCenter
2025-08-26 09:26:05.905210:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:05.905210:   - 数据长度: 8
2025-08-26 09:26:05.905210:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:05.905210:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:05.905210: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:05.906210:   - 发现标签数量: 2
2025-08-26 09:26:05.906210:   - 标签详情:
2025-08-26 09:26:05.906210:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:05.906210:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:05.906210: RFID扫描: 发现 2 个标签
2025-08-26 09:26:05.906210: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:05.906210: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:05.906210: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:05.907210:   - UID: E004015305F83C8E
2025-08-26 09:26:05.907210:   - Data: E004015305F83C8E
2025-08-26 09:26:05.907210:   - EventType: 1
2025-08-26 09:26:05.907210:   - Direction: 0
2025-08-26 09:26:05.907210:   - Antenna: 1
2025-08-26 09:26:05.907210:   - TagFrequency: 0
2025-08-26 09:26:05.907210: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:05.907210:   - UID: E004015305F83C8E
2025-08-26 09:26:05.908210:   - Data: E004015305F83C8E
2025-08-26 09:26:05.908210:   - EventType: 1
2025-08-26 09:26:05.908210:   - Direction: 0
2025-08-26 09:26:05.908210:   - Antenna: 1
2025-08-26 09:26:05.908210:   - TagFrequency: 0
2025-08-26 09:26:06.401201: 🔄 开始RFID轮询检查...
2025-08-26 09:26:06.401201: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:06.401201: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:06.401201: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:06.401201: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:06.403202: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:06.403202:   - 设备句柄: 1495381146272
2025-08-26 09:26:06.403202:   - FetchRecords返回值: 0
2025-08-26 09:26:06.404202:   - 报告数量: 2
2025-08-26 09:26:06.404202:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:06.404202:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:06.404202:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:06.404202:   - 设备类型: LSGControlCenter
2025-08-26 09:26:06.404202:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:06.404202:   - 数据长度: 8
2025-08-26 09:26:06.405201:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:06.405201:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:06.405201:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:06.405201:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:06.405201:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:06.405201:   - 设备类型: LSGControlCenter
2025-08-26 09:26:06.405201:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:06.406201:   - 数据长度: 8
2025-08-26 09:26:06.406201:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:06.406201:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:06.406201: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:06.406201:   - 发现标签数量: 2
2025-08-26 09:26:06.406201:   - 标签详情:
2025-08-26 09:26:06.406201:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:06.406201:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:06.407201: RFID扫描: 发现 2 个标签
2025-08-26 09:26:06.407201: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:06.407201: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:06.407201: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:06.407201:   - UID: E004015305F83C8E
2025-08-26 09:26:06.407201:   - Data: E004015305F83C8E
2025-08-26 09:26:06.407201:   - EventType: 1
2025-08-26 09:26:06.407201:   - Direction: 0
2025-08-26 09:26:06.408201:   - Antenna: 1
2025-08-26 09:26:06.408201:   - TagFrequency: 0
2025-08-26 09:26:06.408201: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:06.408201:   - UID: E004015305F83C8E
2025-08-26 09:26:06.408201:   - Data: E004015305F83C8E
2025-08-26 09:26:06.408201:   - EventType: 1
2025-08-26 09:26:06.408201:   - Direction: 0
2025-08-26 09:26:06.408201:   - Antenna: 1
2025-08-26 09:26:06.409201:   - TagFrequency: 0
2025-08-26 09:26:06.900193: 🔄 开始RFID轮询检查...
2025-08-26 09:26:06.900193: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:06.900193: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:06.900193: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:06.900193: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:06.903193: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:06.903193:   - 设备句柄: 1495381146272
2025-08-26 09:26:06.903193:   - FetchRecords返回值: 0
2025-08-26 09:26:06.903193:   - 报告数量: 2
2025-08-26 09:26:06.903193:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:06.904193:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:06.904193:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:06.904193:   - 设备类型: LSGControlCenter
2025-08-26 09:26:06.904193:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:06.904193:   - 数据长度: 8
2025-08-26 09:26:06.904193:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:06.904193:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:06.904193:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:06.905193:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:06.905193:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:06.905193:   - 设备类型: LSGControlCenter
2025-08-26 09:26:06.905193:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:06.905193:   - 数据长度: 8
2025-08-26 09:26:06.905193:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:06.905193:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:06.905193: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:06.906193:   - 发现标签数量: 2
2025-08-26 09:26:06.906193:   - 标签详情:
2025-08-26 09:26:06.906193:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:06.906193:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:06.906193: RFID扫描: 发现 2 个标签
2025-08-26 09:26:06.906193: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:06.906193: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:06.906193: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:06.906193:   - UID: E004015305F83C8E
2025-08-26 09:26:06.907192:   - Data: E004015305F83C8E
2025-08-26 09:26:06.907192:   - EventType: 1
2025-08-26 09:26:06.907192:   - Direction: 0
2025-08-26 09:26:06.907192:   - Antenna: 1
2025-08-26 09:26:06.907192:   - TagFrequency: 0
2025-08-26 09:26:06.907192: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:06.907192:   - UID: E004015305F83C8E
2025-08-26 09:26:06.907192:   - Data: E004015305F83C8E
2025-08-26 09:26:06.908192:   - EventType: 1
2025-08-26 09:26:06.908192:   - Direction: 0
2025-08-26 09:26:06.908192:   - Antenna: 1
2025-08-26 09:26:06.908192:   - TagFrequency: 0
2025-08-26 09:26:07.401184: 🔄 开始RFID轮询检查...
2025-08-26 09:26:07.401184: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:07.401184: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:07.401184: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:07.402184: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:07.403190: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:07.403190:   - 设备句柄: 1495381146272
2025-08-26 09:26:07.404184:   - FetchRecords返回值: 0
2025-08-26 09:26:07.404184:   - 报告数量: 2
2025-08-26 09:26:07.404184:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:07.404184:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:07.404184:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:07.404184:   - 设备类型: LSGControlCenter
2025-08-26 09:26:07.405184:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:07.405184:   - 数据长度: 8
2025-08-26 09:26:07.405184:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:07.405184:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:07.405184:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:07.405184:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:07.405184:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:07.406184:   - 设备类型: LSGControlCenter
2025-08-26 09:26:07.406184:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:07.406184:   - 数据长度: 8
2025-08-26 09:26:07.406184:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:07.407184:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:07.407184: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:07.407184:   - 发现标签数量: 2
2025-08-26 09:26:07.407184:   - 标签详情:
2025-08-26 09:26:07.407184:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:07.407184:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:07.408184: RFID扫描: 发现 2 个标签
2025-08-26 09:26:07.408184: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:07.408184: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:07.408184: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:07.408184:   - UID: E004015305F83C8E
2025-08-26 09:26:07.408184:   - Data: E004015305F83C8E
2025-08-26 09:26:07.408184:   - EventType: 1
2025-08-26 09:26:07.409184:   - Direction: 0
2025-08-26 09:26:07.409184:   - Antenna: 1
2025-08-26 09:26:07.409184:   - TagFrequency: 0
2025-08-26 09:26:07.409184: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:07.409184:   - UID: E004015305F83C8E
2025-08-26 09:26:07.409184:   - Data: E004015305F83C8E
2025-08-26 09:26:07.409184:   - EventType: 1
2025-08-26 09:26:07.409184:   - Direction: 0
2025-08-26 09:26:07.410184:   - Antenna: 1
2025-08-26 09:26:07.410184:   - TagFrequency: 0
2025-08-26 09:26:07.900175: 🔄 开始RFID轮询检查...
2025-08-26 09:26:07.900175: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:07.901181: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:07.901181: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:07.901181: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:07.903175: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:07.903175:   - 设备句柄: 1495381146272
2025-08-26 09:26:07.904176:   - FetchRecords返回值: 0
2025-08-26 09:26:07.904176:   - 报告数量: 2
2025-08-26 09:26:07.904176:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:07.904176:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:07.905176:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:07.905176:   - 设备类型: LSGControlCenter
2025-08-26 09:26:07.905176:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:07.905176:   - 数据长度: 8
2025-08-26 09:26:07.905176:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:07.905176:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:07.906176:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:07.906176:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:07.906176:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:07.906176:   - 设备类型: LSGControlCenter
2025-08-26 09:26:07.906176:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:07.906176:   - 数据长度: 8
2025-08-26 09:26:07.906176:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:07.906176:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:07.907175: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:07.907175:   - 发现标签数量: 2
2025-08-26 09:26:07.907175:   - 标签详情:
2025-08-26 09:26:07.907175:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:07.907175:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:07.907175: RFID扫描: 发现 2 个标签
2025-08-26 09:26:07.907175: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:07.908177: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:07.908177: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:07.908177:   - UID: E004015305F83C8E
2025-08-26 09:26:07.908177:   - Data: E004015305F83C8E
2025-08-26 09:26:07.908177:   - EventType: 1
2025-08-26 09:26:07.908177:   - Direction: 0
2025-08-26 09:26:07.908177:   - Antenna: 1
2025-08-26 09:26:07.909176:   - TagFrequency: 0
2025-08-26 09:26:07.909176: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:07.909176:   - UID: E004015305F83C8E
2025-08-26 09:26:07.909176:   - Data: E004015305F83C8E
2025-08-26 09:26:07.909176:   - EventType: 1
2025-08-26 09:26:07.909176:   - Direction: 0
2025-08-26 09:26:07.909176:   - Antenna: 1
2025-08-26 09:26:07.909176:   - TagFrequency: 0
2025-08-26 09:26:08.401166: 🔄 开始RFID轮询检查...
2025-08-26 09:26:08.401166: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:08.401166: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:08.401166: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:08.402166: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:08.403166: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:08.403166:   - 设备句柄: 1495381146272
2025-08-26 09:26:08.403166:   - FetchRecords返回值: 0
2025-08-26 09:26:08.403166:   - 报告数量: 2
2025-08-26 09:26:08.403166:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:08.404166:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:08.404166:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:08.404166:   - 设备类型: LSGControlCenter
2025-08-26 09:26:08.404166:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:08.404166:   - 数据长度: 8
2025-08-26 09:26:08.404166:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:08.404166:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:08.404166:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:08.405167:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:08.405167:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:08.405167:   - 设备类型: LSGControlCenter
2025-08-26 09:26:08.405167:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:08.405167:   - 数据长度: 8
2025-08-26 09:26:08.405167:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:08.405167:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:08.405167: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:08.406166:   - 发现标签数量: 2
2025-08-26 09:26:08.406166:   - 标签详情:
2025-08-26 09:26:08.406166:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:08.406166:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:08.406166: RFID扫描: 发现 2 个标签
2025-08-26 09:26:08.406166: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:08.406166: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:08.406166: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:08.407166:   - UID: E004015305F83C8E
2025-08-26 09:26:08.407166:   - Data: E004015305F83C8E
2025-08-26 09:26:08.407166:   - EventType: 1
2025-08-26 09:26:08.407166:   - Direction: 0
2025-08-26 09:26:08.407166:   - Antenna: 1
2025-08-26 09:26:08.407166:   - TagFrequency: 0
2025-08-26 09:26:08.407166: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:08.407166:   - UID: E004015305F83C8E
2025-08-26 09:26:08.408166:   - Data: E004015305F83C8E
2025-08-26 09:26:08.408166:   - EventType: 1
2025-08-26 09:26:08.408166:   - Direction: 0
2025-08-26 09:26:08.408166:   - Antenna: 1
2025-08-26 09:26:08.408166:   - TagFrequency: 0
2025-08-26 09:26:08.900158: 🔄 开始RFID轮询检查...
2025-08-26 09:26:08.900158: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:08.900158: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:08.901159: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:08.901159: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:08.903158: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:08.903158:   - 设备句柄: 1495381146272
2025-08-26 09:26:08.903158:   - FetchRecords返回值: 0
2025-08-26 09:26:08.904158:   - 报告数量: 2
2025-08-26 09:26:08.904158:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:08.904158:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:08.904158:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:08.904158:   - 设备类型: LSGControlCenter
2025-08-26 09:26:08.904158:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:08.904158:   - 数据长度: 8
2025-08-26 09:26:08.905158:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:08.905158:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:08.905158:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:08.905158:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:08.905158:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:08.905158:   - 设备类型: LSGControlCenter
2025-08-26 09:26:08.905158:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:08.905158:   - 数据长度: 8
2025-08-26 09:26:08.906158:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:08.906158:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:08.906158: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:08.906158:   - 发现标签数量: 2
2025-08-26 09:26:08.906158:   - 标签详情:
2025-08-26 09:26:08.906158:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:08.906158:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:08.907158: RFID扫描: 发现 2 个标签
2025-08-26 09:26:08.907158: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:08.907158: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:08.907158: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:08.907158:   - UID: E004015305F83C8E
2025-08-26 09:26:08.907158:   - Data: E004015305F83C8E
2025-08-26 09:26:08.907158:   - EventType: 1
2025-08-26 09:26:08.907158:   - Direction: 0
2025-08-26 09:26:08.908158:   - Antenna: 1
2025-08-26 09:26:08.908158:   - TagFrequency: 0
2025-08-26 09:26:08.908158: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:08.908158:   - UID: E004015305F83C8E
2025-08-26 09:26:08.908158:   - Data: E004015305F83C8E
2025-08-26 09:26:08.908158:   - EventType: 1
2025-08-26 09:26:08.908158:   - Direction: 0
2025-08-26 09:26:08.908158:   - Antenna: 1
2025-08-26 09:26:08.909158:   - TagFrequency: 0
2025-08-26 09:26:09.401150: 🔄 开始RFID轮询检查...
2025-08-26 09:26:09.401150: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:09.401150: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:09.402149: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:09.402149: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:09.403149: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:09.403149:   - 设备句柄: 1495381146272
2025-08-26 09:26:09.403149:   - FetchRecords返回值: 0
2025-08-26 09:26:09.403149:   - 报告数量: 2
2025-08-26 09:26:09.403149:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:09.404149:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:09.404149:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:09.404149:   - 设备类型: LSGControlCenter
2025-08-26 09:26:09.404149:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:09.404149:   - 数据长度: 8
2025-08-26 09:26:09.404149:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:09.404149:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:09.405149:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:09.405149:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:09.405149:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:09.405149:   - 设备类型: LSGControlCenter
2025-08-26 09:26:09.405149:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:09.405149:   - 数据长度: 8
2025-08-26 09:26:09.405149:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:09.406149:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:09.406149: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:09.406149:   - 发现标签数量: 2
2025-08-26 09:26:09.406149:   - 标签详情:
2025-08-26 09:26:09.406149:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:09.406149:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:09.406149: RFID扫描: 发现 2 个标签
2025-08-26 09:26:09.406149: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:09.407149: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:09.407149: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:09.407149:   - UID: E004015305F83C8E
2025-08-26 09:26:09.407149:   - Data: E004015305F83C8E
2025-08-26 09:26:09.407149:   - EventType: 1
2025-08-26 09:26:09.407149:   - Direction: 0
2025-08-26 09:26:09.407149:   - Antenna: 1
2025-08-26 09:26:09.407149:   - TagFrequency: 0
2025-08-26 09:26:09.408149: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:09.408149:   - UID: E004015305F83C8E
2025-08-26 09:26:09.408149:   - Data: E004015305F83C8E
2025-08-26 09:26:09.408149:   - EventType: 1
2025-08-26 09:26:09.408149:   - Direction: 0
2025-08-26 09:26:09.408149:   - Antenna: 1
2025-08-26 09:26:09.408149:   - TagFrequency: 0
2025-08-26 09:26:09.900140: 🔄 开始RFID轮询检查...
2025-08-26 09:26:09.900140: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:09.900140: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:09.900140: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:09.900140: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:09.903149: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:09.904144:   - 设备句柄: 1495381146272
2025-08-26 09:26:09.904144:   - FetchRecords返回值: 0
2025-08-26 09:26:09.905142:   - 报告数量: 2
2025-08-26 09:26:09.905142:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:09.905142:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:09.906141:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:09.906141:   - 设备类型: LSGControlCenter
2025-08-26 09:26:09.906141:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:09.907141:   - 数据长度: 8
2025-08-26 09:26:09.907141:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:09.907141:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:09.907141:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:09.908141:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:09.908141:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:09.908141:   - 设备类型: LSGControlCenter
2025-08-26 09:26:09.908141:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:09.908141:   - 数据长度: 8
2025-08-26 09:26:09.909141:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:09.909141:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:09.909141: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:09.909141:   - 发现标签数量: 2
2025-08-26 09:26:09.909141:   - 标签详情:
2025-08-26 09:26:09.910140:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:09.910140:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:09.910140: RFID扫描: 发现 2 个标签
2025-08-26 09:26:09.910140: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:09.910140: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:09.911140: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:09.911140:   - UID: E004015305F83C8E
2025-08-26 09:26:09.911140:   - Data: E004015305F83C8E
2025-08-26 09:26:09.911140:   - EventType: 1
2025-08-26 09:26:09.911140:   - Direction: 0
2025-08-26 09:26:09.911140:   - Antenna: 1
2025-08-26 09:26:09.912140:   - TagFrequency: 0
2025-08-26 09:26:09.912140: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:09.912140:   - UID: E004015305F83C8E
2025-08-26 09:26:09.912140:   - Data: E004015305F83C8E
2025-08-26 09:26:09.912140:   - EventType: 1
2025-08-26 09:26:09.912140:   - Direction: 0
2025-08-26 09:26:09.913140:   - Antenna: 1
2025-08-26 09:26:09.913140:   - TagFrequency: 0
2025-08-26 09:26:10.401132: 🔄 开始RFID轮询检查...
2025-08-26 09:26:10.401132: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:10.401132: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:10.401132: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:10.401132: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:10.403131: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:10.403131:   - 设备句柄: 1495381146272
2025-08-26 09:26:10.403131:   - FetchRecords返回值: 0
2025-08-26 09:26:10.403131:   - 报告数量: 2
2025-08-26 09:26:10.403131:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:10.403131:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:10.404132:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:10.404132:   - 设备类型: LSGControlCenter
2025-08-26 09:26:10.404132:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:10.404132:   - 数据长度: 8
2025-08-26 09:26:10.404132:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:10.404132:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:10.404132:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:10.404132:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:10.405132:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:10.405132:   - 设备类型: LSGControlCenter
2025-08-26 09:26:10.405132:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:10.405132:   - 数据长度: 8
2025-08-26 09:26:10.405132:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:10.405132:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:10.405132: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:10.405132:   - 发现标签数量: 2
2025-08-26 09:26:10.406132:   - 标签详情:
2025-08-26 09:26:10.406132:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:10.406132:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:10.406132: RFID扫描: 发现 2 个标签
2025-08-26 09:26:10.406132: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:10.406132: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:10.406132: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:10.406132:   - UID: E004015305F83C8E
2025-08-26 09:26:10.407131:   - Data: E004015305F83C8E
2025-08-26 09:26:10.407131:   - EventType: 1
2025-08-26 09:26:10.407131:   - Direction: 0
2025-08-26 09:26:10.407131:   - Antenna: 1
2025-08-26 09:26:10.407131:   - TagFrequency: 0
2025-08-26 09:26:10.407131: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:10.407131:   - UID: E004015305F83C8E
2025-08-26 09:26:10.407131:   - Data: E004015305F83C8E
2025-08-26 09:26:10.407131:   - EventType: 1
2025-08-26 09:26:10.408131:   - Direction: 0
2025-08-26 09:26:10.408131:   - Antenna: 1
2025-08-26 09:26:10.408131:   - TagFrequency: 0
2025-08-26 09:26:10.900124: 🔄 开始RFID轮询检查...
2025-08-26 09:26:10.900124: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:10.900124: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:10.901123: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:10.901123: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:10.904123: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:10.904123:   - 设备句柄: 1495381146272
2025-08-26 09:26:10.904123:   - FetchRecords返回值: 0
2025-08-26 09:26:10.904123:   - 报告数量: 2
2025-08-26 09:26:10.904123:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:10.905123:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:10.905123:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:10.905123:   - 设备类型: LSGControlCenter
2025-08-26 09:26:10.905123:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:10.905123:   - 数据长度: 8
2025-08-26 09:26:10.905123:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:10.905123:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:10.906123:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:10.906123:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:10.906123:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:10.906123:   - 设备类型: LSGControlCenter
2025-08-26 09:26:10.906123:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:10.906123:   - 数据长度: 8
2025-08-26 09:26:10.906123:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:10.906123:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:10.907123: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:10.907123:   - 发现标签数量: 2
2025-08-26 09:26:10.907123:   - 标签详情:
2025-08-26 09:26:10.907123:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:10.907123:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:10.907123: RFID扫描: 发现 2 个标签
2025-08-26 09:26:10.908128: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:10.908128: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:10.908128: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:10.909123:   - UID: E004015305F83C8E
2025-08-26 09:26:10.909123:   - Data: E004015305F83C8E
2025-08-26 09:26:10.909123:   - EventType: 1
2025-08-26 09:26:10.910123:   - Direction: 0
2025-08-26 09:26:10.910123:   - Antenna: 1
2025-08-26 09:26:10.910123:   - TagFrequency: 0
2025-08-26 09:26:10.911124: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:10.911124:   - UID: E004015305F83C8E
2025-08-26 09:26:10.911124:   - Data: E004015305F83C8E
2025-08-26 09:26:10.912123:   - EventType: 1
2025-08-26 09:26:10.912123:   - Direction: 0
2025-08-26 09:26:10.912123:   - Antenna: 1
2025-08-26 09:26:10.912123:   - TagFrequency: 0
2025-08-26 09:26:11.401114: 🔄 开始RFID轮询检查...
2025-08-26 09:26:11.401114: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:11.401114: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:11.401114: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:11.402114: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:11.403114: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:11.403114:   - 设备句柄: 1495381146272
2025-08-26 09:26:11.403114:   - FetchRecords返回值: 0
2025-08-26 09:26:11.403114:   - 报告数量: 2
2025-08-26 09:26:11.403114:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:11.403114:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:11.404114:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:11.404114:   - 设备类型: LSGControlCenter
2025-08-26 09:26:11.404114:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:11.404114:   - 数据长度: 8
2025-08-26 09:26:11.404114:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:11.404114:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:11.405114:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:11.405114:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:11.405114:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:11.405114:   - 设备类型: LSGControlCenter
2025-08-26 09:26:11.405114:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:11.405114:   - 数据长度: 8
2025-08-26 09:26:11.405114:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:11.405114:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:11.406114: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:11.406114:   - 发现标签数量: 2
2025-08-26 09:26:11.406114:   - 标签详情:
2025-08-26 09:26:11.406114:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:11.406114:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:11.406114: RFID扫描: 发现 2 个标签
2025-08-26 09:26:11.406114: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:11.406114: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:11.407114: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:11.407114:   - UID: E004015305F83C8E
2025-08-26 09:26:11.407114:   - Data: E004015305F83C8E
2025-08-26 09:26:11.407114:   - EventType: 1
2025-08-26 09:26:11.407114:   - Direction: 0
2025-08-26 09:26:11.407114:   - Antenna: 1
2025-08-26 09:26:11.407114:   - TagFrequency: 0
2025-08-26 09:26:11.408114: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:11.408114:   - UID: E004015305F83C8E
2025-08-26 09:26:11.408114:   - Data: E004015305F83C8E
2025-08-26 09:26:11.409114:   - EventType: 1
2025-08-26 09:26:11.409114:   - Direction: 0
2025-08-26 09:26:11.409114:   - Antenna: 1
2025-08-26 09:26:11.409114:   - TagFrequency: 0
2025-08-26 09:26:11.901106: 🔄 开始RFID轮询检查...
2025-08-26 09:26:11.901106: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:11.901106: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:11.901106: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:11.902106: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:11.903106: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:11.903106:   - 设备句柄: 1495381146272
2025-08-26 09:26:11.903106:   - FetchRecords返回值: 0
2025-08-26 09:26:11.903106:   - 报告数量: 2
2025-08-26 09:26:11.903106:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:11.903106:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:11.904106:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:11.904106:   - 设备类型: LSGControlCenter
2025-08-26 09:26:11.904106:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:11.904106:   - 数据长度: 8
2025-08-26 09:26:11.904106:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:11.904106:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:11.904106:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:11.905105:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:11.905105:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:11.905105:   - 设备类型: LSGControlCenter
2025-08-26 09:26:11.905105:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:11.905105:   - 数据长度: 8
2025-08-26 09:26:11.905105:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:11.905105:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:11.905105: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:11.906105:   - 发现标签数量: 2
2025-08-26 09:26:11.906105:   - 标签详情:
2025-08-26 09:26:11.906105:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:11.906105:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:11.906105: RFID扫描: 发现 2 个标签
2025-08-26 09:26:11.906105: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:11.906105: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:11.906105: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:11.907105:   - UID: E004015305F83C8E
2025-08-26 09:26:11.907105:   - Data: E004015305F83C8E
2025-08-26 09:26:11.907105:   - EventType: 1
2025-08-26 09:26:11.907105:   - Direction: 0
2025-08-26 09:26:11.907105:   - Antenna: 1
2025-08-26 09:26:11.907105:   - TagFrequency: 0
2025-08-26 09:26:11.907105: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:11.907105:   - UID: E004015305F83C8E
2025-08-26 09:26:11.908105:   - Data: E004015305F83C8E
2025-08-26 09:26:11.908105:   - EventType: 1
2025-08-26 09:26:11.908105:   - Direction: 0
2025-08-26 09:26:11.908105:   - Antenna: 1
2025-08-26 09:26:11.908105:   - TagFrequency: 0
2025-08-26 09:26:12.401097: 🔄 开始RFID轮询检查...
2025-08-26 09:26:12.401097: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:12.402099: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:12.402099: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:12.402099: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:12.403097: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:12.403097:   - 设备句柄: 1495381146272
2025-08-26 09:26:12.403097:   - FetchRecords返回值: 0
2025-08-26 09:26:12.403097:   - 报告数量: 2
2025-08-26 09:26:12.404097:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:12.404097:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:12.404097:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:12.404097:   - 设备类型: LSGControlCenter
2025-08-26 09:26:12.404097:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:12.404097:   - 数据长度: 8
2025-08-26 09:26:12.404097:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:12.404097:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:12.405097:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:12.405097:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:12.405097:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:12.405097:   - 设备类型: LSGControlCenter
2025-08-26 09:26:12.405097:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:12.405097:   - 数据长度: 8
2025-08-26 09:26:12.405097:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:12.406097:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:12.406097: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:12.406097:   - 发现标签数量: 2
2025-08-26 09:26:12.406097:   - 标签详情:
2025-08-26 09:26:12.406097:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:12.406097:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:12.406097: RFID扫描: 发现 2 个标签
2025-08-26 09:26:12.406097: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:12.407097: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:12.407097: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:12.407097:   - UID: E004015305F83C8E
2025-08-26 09:26:12.407097:   - Data: E004015305F83C8E
2025-08-26 09:26:12.407097:   - EventType: 1
2025-08-26 09:26:12.407097:   - Direction: 0
2025-08-26 09:26:12.407097:   - Antenna: 1
2025-08-26 09:26:12.407097:   - TagFrequency: 0
2025-08-26 09:26:12.408097: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:12.408097:   - UID: E004015305F83C8E
2025-08-26 09:26:12.408097:   - Data: E004015305F83C8E
2025-08-26 09:26:12.408097:   - EventType: 1
2025-08-26 09:26:12.408097:   - Direction: 0
2025-08-26 09:26:12.408097:   - Antenna: 1
2025-08-26 09:26:12.408097:   - TagFrequency: 0
2025-08-26 09:26:12.900088: 🔄 开始RFID轮询检查...
2025-08-26 09:26:12.900088: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:12.900088: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:12.900088: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:12.901088: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:12.903088: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:12.903088:   - 设备句柄: 1495381146272
2025-08-26 09:26:12.903088:   - FetchRecords返回值: 0
2025-08-26 09:26:12.903088:   - 报告数量: 2
2025-08-26 09:26:12.903088:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:12.904088:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:12.904088:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:12.904088:   - 设备类型: LSGControlCenter
2025-08-26 09:26:12.904088:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:12.904088:   - 数据长度: 8
2025-08-26 09:26:12.904088:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:12.904088:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:12.904088:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:12.905088:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:12.905088:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:12.905088:   - 设备类型: LSGControlCenter
2025-08-26 09:26:12.905088:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:12.905088:   - 数据长度: 8
2025-08-26 09:26:12.905088:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:12.905088:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:12.905088: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:12.906088:   - 发现标签数量: 2
2025-08-26 09:26:12.906088:   - 标签详情:
2025-08-26 09:26:12.906088:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:12.906088:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:12.906088: RFID扫描: 发现 2 个标签
2025-08-26 09:26:12.906088: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:12.906088: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:12.906088: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:12.907088:   - UID: E004015305F83C8E
2025-08-26 09:26:12.907088:   - Data: E004015305F83C8E
2025-08-26 09:26:12.907088:   - EventType: 1
2025-08-26 09:26:12.907088:   - Direction: 0
2025-08-26 09:26:12.907088:   - Antenna: 1
2025-08-26 09:26:12.907088:   - TagFrequency: 0
2025-08-26 09:26:12.907088: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:12.907088:   - UID: E004015305F83C8E
2025-08-26 09:26:12.908088:   - Data: E004015305F83C8E
2025-08-26 09:26:12.908088:   - EventType: 1
2025-08-26 09:26:12.908088:   - Direction: 0
2025-08-26 09:26:12.908088:   - Antenna: 1
2025-08-26 09:26:12.908088:   - TagFrequency: 0
2025-08-26 09:26:13.401079: 🔄 开始RFID轮询检查...
2025-08-26 09:26:13.401079: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:13.401079: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:13.401079: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:13.401079: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:13.403079: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:13.403079:   - 设备句柄: 1495381146272
2025-08-26 09:26:13.403079:   - FetchRecords返回值: 0
2025-08-26 09:26:13.403079:   - 报告数量: 2
2025-08-26 09:26:13.403079:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:13.404080:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:13.404080:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:13.404080:   - 设备类型: LSGControlCenter
2025-08-26 09:26:13.404080:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:13.404080:   - 数据长度: 8
2025-08-26 09:26:13.404080:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:13.404080:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:13.404080:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:13.405079:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:13.405079:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:13.405079:   - 设备类型: LSGControlCenter
2025-08-26 09:26:13.405079:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:13.405079:   - 数据长度: 8
2025-08-26 09:26:13.405079:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:13.405079:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:13.405079: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:13.406079:   - 发现标签数量: 2
2025-08-26 09:26:13.406079:   - 标签详情:
2025-08-26 09:26:13.406079:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:13.406079:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:13.406079: RFID扫描: 发现 2 个标签
2025-08-26 09:26:13.406079: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:13.406079: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:13.406079: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:13.407079:   - UID: E004015305F83C8E
2025-08-26 09:26:13.407079:   - Data: E004015305F83C8E
2025-08-26 09:26:13.407079:   - EventType: 1
2025-08-26 09:26:13.407079:   - Direction: 0
2025-08-26 09:26:13.407079:   - Antenna: 1
2025-08-26 09:26:13.407079:   - TagFrequency: 0
2025-08-26 09:26:13.407079: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:13.407079:   - UID: E004015305F83C8E
2025-08-26 09:26:13.408079:   - Data: E004015305F83C8E
2025-08-26 09:26:13.408079:   - EventType: 1
2025-08-26 09:26:13.408079:   - Direction: 0
2025-08-26 09:26:13.408079:   - Antenna: 1
2025-08-26 09:26:13.408079:   - TagFrequency: 0
2025-08-26 09:26:13.901071: 🔄 开始RFID轮询检查...
2025-08-26 09:26:13.901071: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:13.901071: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:13.901071: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:13.901071: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:13.903071: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:13.903071:   - 设备句柄: 1495381146272
2025-08-26 09:26:13.903071:   - FetchRecords返回值: 0
2025-08-26 09:26:13.903071:   - 报告数量: 2
2025-08-26 09:26:13.903071:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:13.903071:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:13.904071:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:13.904071:   - 设备类型: LSGControlCenter
2025-08-26 09:26:13.904071:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:13.904071:   - 数据长度: 8
2025-08-26 09:26:13.904071:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:13.904071:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:13.904071:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:13.905071:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:13.905071:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:13.905071:   - 设备类型: LSGControlCenter
2025-08-26 09:26:13.905071:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:13.905071:   - 数据长度: 8
2025-08-26 09:26:13.905071:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:13.905071:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:13.905071: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:13.905071:   - 发现标签数量: 2
2025-08-26 09:26:13.906071:   - 标签详情:
2025-08-26 09:26:13.906071:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:13.906071:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:13.906071: RFID扫描: 发现 2 个标签
2025-08-26 09:26:13.906071: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:13.906071: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:13.906071: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:13.906071:   - UID: E004015305F83C8E
2025-08-26 09:26:13.907071:   - Data: E004015305F83C8E
2025-08-26 09:26:13.907071:   - EventType: 1
2025-08-26 09:26:13.907071:   - Direction: 0
2025-08-26 09:26:13.907071:   - Antenna: 1
2025-08-26 09:26:13.907071:   - TagFrequency: 0
2025-08-26 09:26:13.907071: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:13.907071:   - UID: E004015305F83C8E
2025-08-26 09:26:13.907071:   - Data: E004015305F83C8E
2025-08-26 09:26:13.908071:   - EventType: 1
2025-08-26 09:26:13.908071:   - Direction: 0
2025-08-26 09:26:13.908071:   - Antenna: 1
2025-08-26 09:26:13.908071:   - TagFrequency: 0
2025-08-26 09:26:14.400063: 🔄 开始RFID轮询检查...
2025-08-26 09:26:14.400063: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:14.400063: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:14.400063: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:14.400063: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:14.404070: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:14.404070:   - 设备句柄: 1495381146272
2025-08-26 09:26:14.405063:   - FetchRecords返回值: 0
2025-08-26 09:26:14.405063:   - 报告数量: 2
2025-08-26 09:26:14.405063:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:14.405063:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:14.406064:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:14.406064:   - 设备类型: LSGControlCenter
2025-08-26 09:26:14.406064:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:14.406064:   - 数据长度: 8
2025-08-26 09:26:14.406064:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:14.406064:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:14.406064:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:14.407062:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:14.407062:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:14.407062:   - 设备类型: LSGControlCenter
2025-08-26 09:26:14.407062:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:14.407062:   - 数据长度: 8
2025-08-26 09:26:14.407062:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:14.407062:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:14.408062: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:14.408062:   - 发现标签数量: 2
2025-08-26 09:26:14.408062:   - 标签详情:
2025-08-26 09:26:14.408062:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:14.408062:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:14.408062: RFID扫描: 发现 2 个标签
2025-08-26 09:26:14.409063: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:14.409063: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:14.409063: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:14.409063:   - UID: E004015305F83C8E
2025-08-26 09:26:14.410062:   - Data: E004015305F83C8E
2025-08-26 09:26:14.410062:   - EventType: 1
2025-08-26 09:26:14.410062:   - Direction: 0
2025-08-26 09:26:14.410062:   - Antenna: 1
2025-08-26 09:26:14.410062:   - TagFrequency: 0
2025-08-26 09:26:14.410062: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:14.411062:   - UID: E004015305F83C8E
2025-08-26 09:26:14.411062:   - Data: E004015305F83C8E
2025-08-26 09:26:14.411062:   - EventType: 1
2025-08-26 09:26:14.411062:   - Direction: 0
2025-08-26 09:26:14.411062:   - Antenna: 1
2025-08-26 09:26:14.411062:   - TagFrequency: 0
2025-08-26 09:26:14.706057: 接收到数据: aa 00 c8 80 00 00 27 82
2025-08-26 09:26:14.706057: 🔍 接收到串口数据: aa 00 c8 80 00 00 27 82
2025-08-26 09:26:14.706057: 🔍 数据长度: 8 字节
2025-08-26 09:26:14.707057: 🔍 预定义命令列表:
2025-08-26 09:26:14.707057:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 09:26:14.707057:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 09:26:14.707057:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 09:26:14.707057:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 09:26:14.708057:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 09:26:14.708057:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 09:26:14.708057:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 09:26:14.708057:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 09:26:14.708057:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 09:26:14.708057:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 09:26:14.708057: ✅ 解析到闸机命令: GateCommand.exitStart
2025-08-26 09:26:14.709057: 解析到闸机命令: exit_start (出馆开始)
2025-08-26 09:26:14.709057: 收到闸机命令: exit_start (出馆开始)
2025-08-26 09:26:14.709057: 🚪 收到出馆开始命令，等待出馆到位信号...
2025-08-26 09:26:14.709057: 闸机状态变更: GateState.idle -> GateState.exitStarted
2025-08-26 09:26:14.709057: 闸机状态更新: GateState.idle -> GateState.exitStarted
2025-08-26 09:26:14.709057: 📊 流程状态：出馆流程已开始，等待到位信号
2025-08-26 09:26:14.710058: [channel_1] 收到闸机事件: state_changed
2025-08-26 09:26:14.712059: 📨 收到GateCoordinator事件: state_changed
2025-08-26 09:26:14.712059: 闸机状态变更: GateState.exitStarted
2025-08-26 09:26:14.713058: 🎨 处理状态变更UI: exitStarted
2025-08-26 09:26:14.713058: 未处理的状态变更UI: exitStarted
2025-08-26 09:26:14.713058: [channel_1] 收到闸机事件: exit_start
2025-08-26 09:26:14.713058: [channel_1] 主从机扩展：处理出馆开始（请求-响应模式）
2025-08-26 09:26:14.713058: 扫描结果已清空
2025-08-26 09:26:14.713058: 🧹 [channel_1] 已清空RFID服务扫描结果（页面计数重置）
2025-08-26 09:26:14.713058: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 09:26:14.714057: 🧹 开始清空共享扫描池...
2025-08-26 09:26:14.714057: 📊 清空前状态: 大小=1, 内容=[E004015305F83C8E]
2025-08-26 09:26:14.714057: 🔄 重置RFID去重集合...
2025-08-26 09:26:14.714057: 🔄 开始重置已处理条码集合...
2025-08-26 09:26:14.714057: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 09:26:14.714057: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 09:26:14.714057: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:26:14.714057: 📊 当前tagList状态: 1个标签
2025-08-26 09:26:14.714057: 📋 现有标签将被重新处理:
2025-08-26 09:26:14.715057:   [0] barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:14.715057: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:26:14.715057: 🔄 开始RFID轮询检查...
2025-08-26 09:26:14.715057: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=0个
2025-08-26 09:26:14.715057: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:14.715057: 🆕 轮询发现新标签(UID作为条码): E004015305F83C8E
2025-08-26 09:26:14.715057: 📋 添加到已处理列表: 0 -> 1
2025-08-26 09:26:14.715057: 🏷️ 检测到标签: E004015305F83C8E
2025-08-26 09:26:14.716057: 📊 当前扫描状态: 扫描中=true, 已扫描=1个
2025-08-26 09:26:14.716057: 📋 已扫描列表: [E004015305F83C8E]
2025-08-26 09:26:14.716057: ✅ 条码已发送到barcodeStream，将进入共享池: E004015305F83C8E
2025-08-26 09:26:14.716057: 📡 barcodeStream监听器数量: 有监听器
2025-08-26 09:26:14.716057: ✅ 轮询完成: 发现1个新标签，总计已处理1个标签
2025-08-26 09:26:14.716057: 📋 当前已处理标签列表: [E004015305F83C8E]
2025-08-26 09:26:14.716057: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 09:26:14.716057: 📡 RFID扫描状态（清空后）: isScanning=true
2025-08-26 09:26:14.716057: ✅ 共享扫描池已清空: 1 -> 0
2025-08-26 09:26:14.717056: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 09:26:14.717056: 清空RFID扫描缓冲区...
2025-08-26 09:26:14.717056: 跳过HWTagProvider清空，保持标签列表用于轮询
2025-08-26 09:26:14.717056: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 09:26:14.717056: 🔄 开始重置已处理条码集合...
2025-08-26 09:26:14.717056: 📊 重置前状态: 大小=1, 内容=[E004015305F83C8E]
2025-08-26 09:26:14.717056: ✅ 已处理条码集合已重置: 1 -> 0
2025-08-26 09:26:14.717056: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:26:14.717056: 📊 当前tagList状态: 1个标签
2025-08-26 09:26:14.717056: 📋 现有标签将被重新处理:
2025-08-26 09:26:14.718057:   [0] barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:14.718057: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:26:14.718057: 🔄 开始RFID轮询检查...
2025-08-26 09:26:14.718057: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=0个
2025-08-26 09:26:14.718057: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:14.718057: 🆕 轮询发现新标签(UID作为条码): E004015305F83C8E
2025-08-26 09:26:14.718057: 📋 添加到已处理列表: 0 -> 1
2025-08-26 09:26:14.718057: 🏷️ 检测到标签: E004015305F83C8E
2025-08-26 09:26:14.718057: 📊 当前扫描状态: 扫描中=true, 已扫描=1个
2025-08-26 09:26:14.718057: 📋 已扫描列表: [E004015305F83C8E]
2025-08-26 09:26:14.718057: ✅ 条码已发送到barcodeStream，将进入共享池: E004015305F83C8E
2025-08-26 09:26:14.719056: 📡 barcodeStream监听器数量: 有监听器
2025-08-26 09:26:14.719056: ✅ 轮询完成: 发现1个新标签，总计已处理1个标签
2025-08-26 09:26:14.719056: 📋 当前已处理标签列表: [E004015305F83C8E]
2025-08-26 09:26:14.719056: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 09:26:14.719056: 📨 收到GateCoordinator事件: exit_start
2025-08-26 09:26:14.720057: 页面状态变更: SilencePageState.waitingExit
2025-08-26 09:26:14.720057: 🔄 开始重置已处理条码集合...
2025-08-26 09:26:14.720057: 📊 重置前状态: 大小=1, 内容=[E004015305F83C8E]
2025-08-26 09:26:14.720057: ✅ 已处理条码集合已重置: 1 -> 0
2025-08-26 09:26:14.720057: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:26:14.720057: 📊 当前tagList状态: 1个标签
2025-08-26 09:26:14.721056: 📋 现有标签将被重新处理:
2025-08-26 09:26:14.721056:   [0] barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:14.721056: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:26:14.721056: 🔄 开始RFID轮询检查...
2025-08-26 09:26:14.721056: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=0个
2025-08-26 09:26:14.721056: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:14.721056: 🆕 轮询发现新标签(UID作为条码): E004015305F83C8E
2025-08-26 09:26:14.721056: 📋 添加到已处理列表: 0 -> 1
2025-08-26 09:26:14.721056: 🔄 条码已扫描过，跳过: E004015305F83C8E
2025-08-26 09:26:14.721056: ✅ 轮询完成: 发现1个新标签，总计已处理1个标签
2025-08-26 09:26:14.722056: 📋 当前已处理标签列表: [E004015305F83C8E]
2025-08-26 09:26:14.722056: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 09:26:14.722056: 🧹 [channel_1] 主机清空列表1和RFID缓冲区: 清除1个条码
2025-08-26 09:26:14.722056: 🔄 尝试添加条码到共享池: E004015305F83C8E
2025-08-26 09:26:14.722056: 📊 添加前状态: 共享池大小=0, 是否为空=true
2025-08-26 09:26:14.722056: ✅ 成功添加条码到共享池: E004015305F83C8E (总计: 1)
2025-08-26 09:26:14.722056: 📋 当前共享池内容: [E004015305F83C8E]
2025-08-26 09:26:14.722056: 📡 共享池变化通知已发送
2025-08-26 09:26:14.722056: 🔄 尝试添加条码到共享池: E004015305F83C8E
2025-08-26 09:26:14.722056: 📊 添加前状态: 共享池大小=1, 是否为空=false
2025-08-26 09:26:14.723056: 🔄 条码已存在于共享池: E004015305F83C8E (总计: 1)
2025-08-26 09:26:14.723056: 🔄 尝试添加条码到共享池: E004015305F83C8E
2025-08-26 09:26:14.723056: 📊 添加前状态: 共享池大小=1, 是否为空=false
2025-08-26 09:26:14.723056: 🔄 条码已存在于共享池: E004015305F83C8E (总计: 1)
2025-08-26 09:26:14.723056: 🔄 尝试添加条码到共享池: E004015305F83C8E
2025-08-26 09:26:14.723056: 📊 添加前状态: 共享池大小=1, 是否为空=false
2025-08-26 09:26:14.723056: 🔄 条码已存在于共享池: E004015305F83C8E (总计: 1)
2025-08-26 09:26:14.901053: 🔄 开始RFID轮询检查...
2025-08-26 09:26:14.901053: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:14.901053: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:14.901053: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:14.901053: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:14.903053: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:14.903053:   - 设备句柄: 1495381146272
2025-08-26 09:26:14.903053:   - FetchRecords返回值: 0
2025-08-26 09:26:14.903053:   - 报告数量: 2
2025-08-26 09:26:14.903053:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:14.903053:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:14.904053:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:14.904053:   - 设备类型: LSGControlCenter
2025-08-26 09:26:14.904053:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:14.904053:   - 数据长度: 8
2025-08-26 09:26:14.904053:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:14.904053:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:14.904053:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:14.904053:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:14.905053:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:14.905053:   - 设备类型: LSGControlCenter
2025-08-26 09:26:14.905053:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:14.905053:   - 数据长度: 8
2025-08-26 09:26:14.905053:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:14.905053:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:14.905053: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:14.905053:   - 发现标签数量: 2
2025-08-26 09:26:14.906053:   - 标签详情:
2025-08-26 09:26:14.906053:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:14.906053:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:14.906053: RFID扫描: 发现 2 个标签
2025-08-26 09:26:14.906053: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:14.906053: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:14.906053: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:14.906053:   - UID: E004015305F83C8E
2025-08-26 09:26:14.906053:   - Data: E004015305F83C8E
2025-08-26 09:26:14.907053:   - EventType: 1
2025-08-26 09:26:14.907053:   - Direction: 0
2025-08-26 09:26:14.907053:   - Antenna: 1
2025-08-26 09:26:14.907053:   - TagFrequency: 0
2025-08-26 09:26:14.907053: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:14.907053:   - UID: E004015305F83C8E
2025-08-26 09:26:14.907053:   - Data: E004015305F83C8E
2025-08-26 09:26:14.907053:   - EventType: 1
2025-08-26 09:26:14.908053:   - Direction: 0
2025-08-26 09:26:14.908053:   - Antenna: 1
2025-08-26 09:26:14.908053:   - TagFrequency: 0
2025-08-26 09:26:15.401045: 🔄 开始RFID轮询检查...
2025-08-26 09:26:15.401045: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:15.401045: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:15.402045: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:15.402045: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:15.403046: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:15.403046:   - 设备句柄: 1495381146272
2025-08-26 09:26:15.403046:   - FetchRecords返回值: 0
2025-08-26 09:26:15.404045:   - 报告数量: 2
2025-08-26 09:26:15.404045:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:15.404045:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:15.404045:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:15.405046:   - 设备类型: LSGControlCenter
2025-08-26 09:26:15.405046:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:15.405046:   - 数据长度: 8
2025-08-26 09:26:15.405046:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:15.405046:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:15.405046:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:15.406045:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:15.406045:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:15.406045:   - 设备类型: LSGControlCenter
2025-08-26 09:26:15.406045:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:15.406045:   - 数据长度: 8
2025-08-26 09:26:15.406045:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:15.407046:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:15.407046: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:15.407046:   - 发现标签数量: 2
2025-08-26 09:26:15.407046:   - 标签详情:
2025-08-26 09:26:15.407046:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:15.408045:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:15.408045: RFID扫描: 发现 2 个标签
2025-08-26 09:26:15.408045: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:15.408045: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:15.408045: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:15.408045:   - UID: E004015305F83C8E
2025-08-26 09:26:15.408045:   - Data: E004015305F83C8E
2025-08-26 09:26:15.409045:   - EventType: 1
2025-08-26 09:26:15.409045:   - Direction: 0
2025-08-26 09:26:15.409045:   - Antenna: 1
2025-08-26 09:26:15.409045:   - TagFrequency: 0
2025-08-26 09:26:15.409045: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:15.409045:   - UID: E004015305F83C8E
2025-08-26 09:26:15.409045:   - Data: E004015305F83C8E
2025-08-26 09:26:15.410044:   - EventType: 1
2025-08-26 09:26:15.410044:   - Direction: 0
2025-08-26 09:26:15.410044:   - Antenna: 1
2025-08-26 09:26:15.410044:   - TagFrequency: 0
2025-08-26 09:26:15.721039: 📊 [channel_1] 主机返回当前数据: 1个条码
2025-08-26 09:26:15.901036: 🔄 开始RFID轮询检查...
2025-08-26 09:26:15.901036: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:15.901036: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:15.901036: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:15.901036: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:15.903036: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:15.903036:   - 设备句柄: 1495381146272
2025-08-26 09:26:15.903036:   - FetchRecords返回值: 0
2025-08-26 09:26:15.903036:   - 报告数量: 2
2025-08-26 09:26:15.903036:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:15.903036:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:15.904036:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:15.904036:   - 设备类型: LSGControlCenter
2025-08-26 09:26:15.904036:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:15.904036:   - 数据长度: 8
2025-08-26 09:26:15.904036:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:15.904036:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:15.904036:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:15.905036:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:15.905036:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:15.905036:   - 设备类型: LSGControlCenter
2025-08-26 09:26:15.905036:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:15.905036:   - 数据长度: 8
2025-08-26 09:26:15.905036:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:15.905036:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:15.905036: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:15.906036:   - 发现标签数量: 2
2025-08-26 09:26:15.906036:   - 标签详情:
2025-08-26 09:26:15.906036:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:15.906036:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:15.906036: RFID扫描: 发现 2 个标签
2025-08-26 09:26:15.906036: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:15.906036: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:15.906036: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:15.907036:   - UID: E004015305F83C8E
2025-08-26 09:26:15.907036:   - Data: E004015305F83C8E
2025-08-26 09:26:15.907036:   - EventType: 1
2025-08-26 09:26:15.907036:   - Direction: 0
2025-08-26 09:26:15.907036:   - Antenna: 1
2025-08-26 09:26:15.907036:   - TagFrequency: 0
2025-08-26 09:26:15.907036: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:15.907036:   - UID: E004015305F83C8E
2025-08-26 09:26:15.908036:   - Data: E004015305F83C8E
2025-08-26 09:26:15.908036:   - EventType: 1
2025-08-26 09:26:15.908036:   - Direction: 0
2025-08-26 09:26:15.908036:   - Antenna: 1
2025-08-26 09:26:15.908036:   - TagFrequency: 0
2025-08-26 09:26:16.401028: 🔄 开始RFID轮询检查...
2025-08-26 09:26:16.401028: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:16.401028: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:16.401028: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:16.401028: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:16.403028: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:16.404029:   - 设备句柄: 1495381146272
2025-08-26 09:26:16.405029:   - FetchRecords返回值: 0
2025-08-26 09:26:16.405029:   - 报告数量: 2
2025-08-26 09:26:16.405029:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:16.405029:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:16.406028:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:16.406028:   - 设备类型: LSGControlCenter
2025-08-26 09:26:16.406028:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:16.406028:   - 数据长度: 8
2025-08-26 09:26:16.407027:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:16.407027:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:16.407027:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:16.407027:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:16.407027:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:16.408027:   - 设备类型: LSGControlCenter
2025-08-26 09:26:16.408027:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:16.408027:   - 数据长度: 8
2025-08-26 09:26:16.408027:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:16.408027:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:16.408027: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:16.409028:   - 发现标签数量: 2
2025-08-26 09:26:16.409028:   - 标签详情:
2025-08-26 09:26:16.409028:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:16.409028:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:16.409028: RFID扫描: 发现 2 个标签
2025-08-26 09:26:16.409028: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:16.410027: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:16.410027: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:16.410027:   - UID: E004015305F83C8E
2025-08-26 09:26:16.410027:   - Data: E004015305F83C8E
2025-08-26 09:26:16.410027:   - EventType: 1
2025-08-26 09:26:16.410027:   - Direction: 0
2025-08-26 09:26:16.410027:   - Antenna: 1
2025-08-26 09:26:16.411027:   - TagFrequency: 0
2025-08-26 09:26:16.411027: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:16.411027:   - UID: E004015305F83C8E
2025-08-26 09:26:16.411027:   - Data: E004015305F83C8E
2025-08-26 09:26:16.411027:   - EventType: 1
2025-08-26 09:26:16.411027:   - Direction: 0
2025-08-26 09:26:16.411027:   - Antenna: 1
2025-08-26 09:26:16.411027:   - TagFrequency: 0
2025-08-26 09:26:16.901019: 🔄 开始RFID轮询检查...
2025-08-26 09:26:16.901019: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:16.902020: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:16.902020: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:16.902020: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:16.903019: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:16.903019:   - 设备句柄: 1495381146272
2025-08-26 09:26:16.903019:   - FetchRecords返回值: 0
2025-08-26 09:26:16.904019:   - 报告数量: 2
2025-08-26 09:26:16.904019:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:16.904019:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:16.904019:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:16.904019:   - 设备类型: LSGControlCenter
2025-08-26 09:26:16.904019:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:16.905020:   - 数据长度: 8
2025-08-26 09:26:16.905020:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:16.905020:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:16.905020:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:16.906019:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:16.906019:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:16.906019:   - 设备类型: LSGControlCenter
2025-08-26 09:26:16.906019:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:16.906019:   - 数据长度: 8
2025-08-26 09:26:16.907036:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:16.907036:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:16.907036: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:16.907036:   - 发现标签数量: 2
2025-08-26 09:26:16.908019:   - 标签详情:
2025-08-26 09:26:16.908019:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:16.908019:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:16.908019: RFID扫描: 发现 2 个标签
2025-08-26 09:26:16.909020: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:16.909020: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:16.909020: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:16.909020:   - UID: E004015305F83C8E
2025-08-26 09:26:16.909020:   - Data: E004015305F83C8E
2025-08-26 09:26:16.909020:   - EventType: 1
2025-08-26 09:26:16.910019:   - Direction: 0
2025-08-26 09:26:16.910019:   - Antenna: 1
2025-08-26 09:26:16.910019:   - TagFrequency: 0
2025-08-26 09:26:16.910019: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:16.910019:   - UID: E004015305F83C8E
2025-08-26 09:26:16.910019:   - Data: E004015305F83C8E
2025-08-26 09:26:16.910019:   - EventType: 1
2025-08-26 09:26:16.910019:   - Direction: 0
2025-08-26 09:26:16.911019:   - Antenna: 1
2025-08-26 09:26:16.911019:   - TagFrequency: 0
2025-08-26 09:26:16.926018: 接收到数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 09:26:16.926018: 🔍 接收到串口数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 09:26:16.926018: 🔍 数据长度: 8 字节
2025-08-26 09:26:16.926018: 🔍 预定义命令列表:
2025-08-26 09:26:16.926018:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 09:26:16.927018:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 09:26:16.927018:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 09:26:16.927018:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 09:26:16.927018:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 09:26:16.927018:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 09:26:16.927018:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 09:26:16.927018:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 09:26:16.927018:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 09:26:16.927018:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 09:26:16.927018: ✅ 解析到闸机命令: GateCommand.reachPosition
2025-08-26 09:26:16.928018: 解析到闸机命令: position_reached (到达指定位置)
2025-08-26 09:26:16.929021: 收到闸机命令: position_reached (到达指定位置)
2025-08-26 09:26:16.929021: 📍 收到到位信号，当前状态: GateState.exitStarted
2025-08-26 09:26:16.929021: 📊 流程状态：进馆=false, 出馆=true
2025-08-26 09:26:16.929021: 📊 待处理认证：进馆=false, 出馆=false
2025-08-26 09:26:16.929021: 🚪 出馆到位信号，启动认证和10秒数据收集...
2025-08-26 09:26:16.930018: 闸机状态变更: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 09:26:16.930018: 闸机状态更新: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 09:26:16.930018: 🔐 启动出馆认证系统（不关注结果）...
2025-08-26 09:26:16.930018: 闸机状态变更: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 09:26:16.930018: 闸机状态更新: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 09:26:16.930018: 认证监听已在运行中
2025-08-26 09:26:16.930018: ✅ 出馆认证系统已启动
2025-08-26 09:26:16.930018: 🚀 启动出馆10秒数据收集...
2025-08-26 09:26:16.931018: 🔧 启动10秒计时器，当前时间: 2025-08-26 09:26:16.928018
2025-08-26 09:26:16.931018: 🔧 10秒计时器已设置，计时器对象: Instance of '_Timer'
2025-08-26 09:26:16.931018: 📡 开始从共享池收集数据...
2025-08-26 09:26:16.931018: 🔧 RFID扫描已在运行，只清空共享池准备收集新数据
2025-08-26 09:26:16.931018: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 09:26:16.931018: 🧹 开始清空共享扫描池...
2025-08-26 09:26:16.931018: 📊 清空前状态: 大小=1, 内容=[E004015305F83C8E]
2025-08-26 09:26:16.931018: 🔄 重置RFID去重集合...
2025-08-26 09:26:16.931018: 🔄 开始重置已处理条码集合...
2025-08-26 09:26:16.931018: 📊 重置前状态: 大小=1, 内容=[E004015305F83C8E]
2025-08-26 09:26:16.931018: ✅ 已处理条码集合已重置: 1 -> 0
2025-08-26 09:26:16.932018: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:26:16.932018: 📊 当前tagList状态: 1个标签
2025-08-26 09:26:16.932018: 📋 现有标签将被重新处理:
2025-08-26 09:26:16.932018:   [0] barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:16.932018: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:26:16.932018: 🔄 开始RFID轮询检查...
2025-08-26 09:26:16.932018: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=0个
2025-08-26 09:26:16.932018: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:16.932018: 🆕 轮询发现新标签(UID作为条码): E004015305F83C8E
2025-08-26 09:26:16.932018: 📋 添加到已处理列表: 0 -> 1
2025-08-26 09:26:16.932018: 🔄 条码已扫描过，跳过: E004015305F83C8E
2025-08-26 09:26:16.933018: ✅ 轮询完成: 发现1个新标签，总计已处理1个标签
2025-08-26 09:26:16.933018: 📋 当前已处理标签列表: [E004015305F83C8E]
2025-08-26 09:26:16.933018: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 09:26:16.933018: 📡 RFID扫描状态（清空后）: isScanning=true
2025-08-26 09:26:16.933018: ✅ 共享扫描池已清空: 1 -> 0
2025-08-26 09:26:16.933018: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 09:26:16.933018: 清空RFID扫描缓冲区...
2025-08-26 09:26:16.933018: 跳过HWTagProvider清空，保持标签列表用于轮询
2025-08-26 09:26:16.933018: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 09:26:16.933018: 🔄 开始重置已处理条码集合...
2025-08-26 09:26:16.933018: 📊 重置前状态: 大小=1, 内容=[E004015305F83C8E]
2025-08-26 09:26:16.934018: ✅ 已处理条码集合已重置: 1 -> 0
2025-08-26 09:26:16.934018: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:26:16.934018: 📊 当前tagList状态: 1个标签
2025-08-26 09:26:16.934018: 📋 现有标签将被重新处理:
2025-08-26 09:26:16.934018:   [0] barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:16.934018: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:26:16.934018: 🔄 开始RFID轮询检查...
2025-08-26 09:26:16.934018: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=0个
2025-08-26 09:26:16.934018: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:16.934018: 🆕 轮询发现新标签(UID作为条码): E004015305F83C8E
2025-08-26 09:26:16.935018: 📋 添加到已处理列表: 0 -> 1
2025-08-26 09:26:16.935018: 🏷️ 检测到标签: E004015305F83C8E
2025-08-26 09:26:16.935018: 📊 当前扫描状态: 扫描中=true, 已扫描=1个
2025-08-26 09:26:16.935018: 📋 已扫描列表: [E004015305F83C8E]
2025-08-26 09:26:16.935018: ✅ 条码已发送到barcodeStream，将进入共享池: E004015305F83C8E
2025-08-26 09:26:16.935018: 📡 barcodeStream监听器数量: 有监听器
2025-08-26 09:26:16.935018: ✅ 轮询完成: 发现1个新标签，总计已处理1个标签
2025-08-26 09:26:16.935018: 📋 当前已处理标签列表: [E004015305F83C8E]
2025-08-26 09:26:16.935018: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 09:26:16.936018: [channel_1] 收到闸机事件: state_changed
2025-08-26 09:26:16.936018: 📨 收到GateCoordinator事件: state_changed
2025-08-26 09:26:16.936018: 闸机状态变更: GateState.exitWaitingAuth
2025-08-26 09:26:16.936018: 🎨 处理状态变更UI: exitWaitingAuth
2025-08-26 09:26:16.936018: 未处理的状态变更UI: exitWaitingAuth
2025-08-26 09:26:16.936018: 🔄 开始重置已处理条码集合...
2025-08-26 09:26:16.937018: 📊 重置前状态: 大小=1, 内容=[E004015305F83C8E]
2025-08-26 09:26:16.937018: ✅ 已处理条码集合已重置: 1 -> 0
2025-08-26 09:26:16.937018: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 09:26:16.937018: 📊 当前tagList状态: 1个标签
2025-08-26 09:26:16.937018: 📋 现有标签将被重新处理:
2025-08-26 09:26:16.937018:   [0] barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:16.937018: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 09:26:16.937018: 🔄 开始RFID轮询检查...
2025-08-26 09:26:16.937018: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=0个
2025-08-26 09:26:16.937018: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:16.938018: 🆕 轮询发现新标签(UID作为条码): E004015305F83C8E
2025-08-26 09:26:16.938018: 📋 添加到已处理列表: 0 -> 1
2025-08-26 09:26:16.938018: 🔄 条码已扫描过，跳过: E004015305F83C8E
2025-08-26 09:26:16.938018: ✅ 轮询完成: 发现1个新标签，总计已处理1个标签
2025-08-26 09:26:16.938018: 📋 当前已处理标签列表: [E004015305F83C8E]
2025-08-26 09:26:16.938018: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 09:26:16.938018: [channel_1] 收到闸机事件: state_changed
2025-08-26 09:26:16.939018: 📨 收到GateCoordinator事件: state_changed
2025-08-26 09:26:16.939018: 闸机状态变更: GateState.exitScanning
2025-08-26 09:26:16.939018: 🎨 处理状态变更UI: exitScanning
2025-08-26 09:26:16.939018: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 09:26:16.939018: 🔄 尝试添加条码到共享池: E004015305F83C8E
2025-08-26 09:26:16.940018: 📊 添加前状态: 共享池大小=0, 是否为空=true
2025-08-26 09:26:16.940018: ✅ 成功添加条码到共享池: E004015305F83C8E (总计: 1)
2025-08-26 09:26:16.940018: 📋 当前共享池内容: [E004015305F83C8E]
2025-08-26 09:26:16.941018: 📡 共享池变化通知已发送
2025-08-26 09:26:16.941018: 🔄 尝试添加条码到共享池: E004015305F83C8E
2025-08-26 09:26:16.941018: 📊 添加前状态: 共享池大小=1, 是否为空=false
2025-08-26 09:26:16.941018: 🔄 条码已存在于共享池: E004015305F83C8E (总计: 1)
2025-08-26 09:26:16.942021: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 09:26:17.401010: 🔄 开始RFID轮询检查...
2025-08-26 09:26:17.401010: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:17.401010: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:17.401010: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:17.401010: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:17.403010: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:17.403010:   - 设备句柄: 1495381146272
2025-08-26 09:26:17.403010:   - FetchRecords返回值: 0
2025-08-26 09:26:17.403010:   - 报告数量: 2
2025-08-26 09:26:17.403010:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:17.403010:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:17.404010:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:17.404010:   - 设备类型: LSGControlCenter
2025-08-26 09:26:17.404010:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:17.404010:   - 数据长度: 8
2025-08-26 09:26:17.404010:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:17.404010:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:17.404010:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:17.405010:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:17.405010:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:17.405010:   - 设备类型: LSGControlCenter
2025-08-26 09:26:17.405010:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:17.405010:   - 数据长度: 8
2025-08-26 09:26:17.405010:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:17.405010:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:17.405010: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:17.406010:   - 发现标签数量: 2
2025-08-26 09:26:17.406010:   - 标签详情:
2025-08-26 09:26:17.406010:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:17.406010:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:17.406010: RFID扫描: 发现 2 个标签
2025-08-26 09:26:17.406010: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:17.406010: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:17.406010: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:17.407010:   - UID: E004015305F83C8E
2025-08-26 09:26:17.407010:   - Data: E004015305F83C8E
2025-08-26 09:26:17.407010:   - EventType: 1
2025-08-26 09:26:17.407010:   - Direction: 0
2025-08-26 09:26:17.407010:   - Antenna: 1
2025-08-26 09:26:17.407010:   - TagFrequency: 0
2025-08-26 09:26:17.408010: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:17.408010:   - UID: E004015305F83C8E
2025-08-26 09:26:17.409015:   - Data: E004015305F83C8E
2025-08-26 09:26:17.409015:   - EventType: 1
2025-08-26 09:26:17.409015:   - Direction: 0
2025-08-26 09:26:17.410011:   - Antenna: 1
2025-08-26 09:26:17.410011:   - TagFrequency: 0
2025-08-26 09:26:17.901001: 🔄 开始RFID轮询检查...
2025-08-26 09:26:17.901001: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:17.901001: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:17.901001: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:17.901001: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:17.903001: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:17.903001:   - 设备句柄: 1495381146272
2025-08-26 09:26:17.903001:   - FetchRecords返回值: 0
2025-08-26 09:26:17.903001:   - 报告数量: 2
2025-08-26 09:26:17.903001:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:17.903001:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:17.904001:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:17.904001:   - 设备类型: LSGControlCenter
2025-08-26 09:26:17.904001:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:17.904001:   - 数据长度: 8
2025-08-26 09:26:17.904001:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:17.904001:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:17.904001:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:17.905001:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:17.905001:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:17.905001:   - 设备类型: LSGControlCenter
2025-08-26 09:26:17.905001:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:17.905001:   - 数据长度: 8
2025-08-26 09:26:17.905001:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:17.905001:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:17.905001: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:17.906001:   - 发现标签数量: 2
2025-08-26 09:26:17.906001:   - 标签详情:
2025-08-26 09:26:17.906001:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:17.906001:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:17.906001: RFID扫描: 发现 2 个标签
2025-08-26 09:26:17.906001: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:17.906001: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:17.906001: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:17.907001:   - UID: E004015305F83C8E
2025-08-26 09:26:17.907001:   - Data: E004015305F83C8E
2025-08-26 09:26:17.907001:   - EventType: 1
2025-08-26 09:26:17.907001:   - Direction: 0
2025-08-26 09:26:17.907001:   - Antenna: 1
2025-08-26 09:26:17.907001:   - TagFrequency: 0
2025-08-26 09:26:17.907001: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:17.907001:   - UID: E004015305F83C8E
2025-08-26 09:26:17.908001:   - Data: E004015305F83C8E
2025-08-26 09:26:17.908001:   - EventType: 1
2025-08-26 09:26:17.908001:   - Direction: 0
2025-08-26 09:26:17.908001:   - Antenna: 1
2025-08-26 09:26:17.908001:   - TagFrequency: 0
2025-08-26 09:26:18.400992: 🔄 开始RFID轮询检查...
2025-08-26 09:26:18.400992: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:18.400992: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:18.401994: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:18.401994: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:18.403001: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:18.403001:   - 设备句柄: 1495381146272
2025-08-26 09:26:18.403993:   - FetchRecords返回值: 0
2025-08-26 09:26:18.403993:   - 报告数量: 2
2025-08-26 09:26:18.403993:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:18.403993:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:18.404998:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:18.404998:   - 设备类型: LSGControlCenter
2025-08-26 09:26:18.404998:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:18.404998:   - 数据长度: 8
2025-08-26 09:26:18.405993:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:18.405993:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:18.405993:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:18.405993:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:18.405993:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:18.406999:   - 设备类型: LSGControlCenter
2025-08-26 09:26:18.406999:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:18.406999:   - 数据长度: 8
2025-08-26 09:26:18.406999:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:18.406999:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:18.407993: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:18.407993:   - 发现标签数量: 2
2025-08-26 09:26:18.407993:   - 标签详情:
2025-08-26 09:26:18.407993:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:18.407993:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:18.407993: RFID扫描: 发现 2 个标签
2025-08-26 09:26:18.407993: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:18.408992: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:18.408992: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:18.408992:   - UID: E004015305F83C8E
2025-08-26 09:26:18.408992:   - Data: E004015305F83C8E
2025-08-26 09:26:18.408992:   - EventType: 1
2025-08-26 09:26:18.408992:   - Direction: 0
2025-08-26 09:26:18.408992:   - Antenna: 1
2025-08-26 09:26:18.409992:   - TagFrequency: 0
2025-08-26 09:26:18.409992: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:18.409992:   - UID: E004015305F83C8E
2025-08-26 09:26:18.409992:   - Data: E004015305F83C8E
2025-08-26 09:26:18.410992:   - EventType: 1
2025-08-26 09:26:18.410992:   - Direction: 0
2025-08-26 09:26:18.410992:   - Antenna: 1
2025-08-26 09:26:18.410992:   - TagFrequency: 0
2025-08-26 09:26:18.899983: 🔄 开始RFID轮询检查...
2025-08-26 09:26:18.899983: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:18.899983: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:18.899983: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:18.899983: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:18.902983: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:18.902983:   - 设备句柄: 1495381146272
2025-08-26 09:26:18.902983:   - FetchRecords返回值: 0
2025-08-26 09:26:18.902983:   - 报告数量: 2
2025-08-26 09:26:18.902983:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:18.902983:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:18.903984:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:18.903984:   - 设备类型: LSGControlCenter
2025-08-26 09:26:18.903984:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:18.903984:   - 数据长度: 8
2025-08-26 09:26:18.903984:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:18.903984:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:18.903984:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:18.903984:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:18.904983:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:18.904983:   - 设备类型: LSGControlCenter
2025-08-26 09:26:18.904983:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:18.904983:   - 数据长度: 8
2025-08-26 09:26:18.904983:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:18.904983:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:18.904983: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:18.904983:   - 发现标签数量: 2
2025-08-26 09:26:18.905983:   - 标签详情:
2025-08-26 09:26:18.905983:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:18.905983:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:18.905983: RFID扫描: 发现 2 个标签
2025-08-26 09:26:18.905983: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:18.905983: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:18.905983: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:18.905983:   - UID: E004015305F83C8E
2025-08-26 09:26:18.906983:   - Data: E004015305F83C8E
2025-08-26 09:26:18.906983:   - EventType: 1
2025-08-26 09:26:18.906983:   - Direction: 0
2025-08-26 09:26:18.906983:   - Antenna: 1
2025-08-26 09:26:18.906983:   - TagFrequency: 0
2025-08-26 09:26:18.906983: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:18.906983:   - UID: E004015305F83C8E
2025-08-26 09:26:18.906983:   - Data: E004015305F83C8E
2025-08-26 09:26:18.907983:   - EventType: 1
2025-08-26 09:26:18.907983:   - Direction: 0
2025-08-26 09:26:18.907983:   - Antenna: 1
2025-08-26 09:26:18.907983:   - TagFrequency: 0
2025-08-26 09:26:19.400975: 🔄 开始RFID轮询检查...
2025-08-26 09:26:19.400975: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:19.400975: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:19.400975: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:19.400975: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:19.402975: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:19.402975:   - 设备句柄: 1495381146272
2025-08-26 09:26:19.403975:   - FetchRecords返回值: 0
2025-08-26 09:26:19.403975:   - 报告数量: 2
2025-08-26 09:26:19.403975:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:19.403975:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:19.404981:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:19.404981:   - 设备类型: LSGControlCenter
2025-08-26 09:26:19.404981:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:19.404981:   - 数据长度: 8
2025-08-26 09:26:19.404981:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:19.404981:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:19.404981:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:19.405975:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:19.405975:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:19.405975:   - 设备类型: LSGControlCenter
2025-08-26 09:26:19.405975:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:19.405975:   - 数据长度: 8
2025-08-26 09:26:19.405975:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:19.406976:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:19.406976: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:19.406976:   - 发现标签数量: 2
2025-08-26 09:26:19.406976:   - 标签详情:
2025-08-26 09:26:19.406976:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:19.407975:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:19.407975: RFID扫描: 发现 2 个标签
2025-08-26 09:26:19.407975: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:19.407975: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:19.407975: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:19.407975:   - UID: E004015305F83C8E
2025-08-26 09:26:19.408975:   - Data: E004015305F83C8E
2025-08-26 09:26:19.408975:   - EventType: 1
2025-08-26 09:26:19.408975:   - Direction: 0
2025-08-26 09:26:19.408975:   - Antenna: 1
2025-08-26 09:26:19.408975:   - TagFrequency: 0
2025-08-26 09:26:19.408975: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:19.408975:   - UID: E004015305F83C8E
2025-08-26 09:26:19.409975:   - Data: E004015305F83C8E
2025-08-26 09:26:19.409975:   - EventType: 1
2025-08-26 09:26:19.409975:   - Direction: 0
2025-08-26 09:26:19.409975:   - Antenna: 1
2025-08-26 09:26:19.409975:   - TagFrequency: 0
2025-08-26 09:26:19.899968: 🔄 开始RFID轮询检查...
2025-08-26 09:26:19.899968: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:19.900971: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:19.900971: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:19.900971: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:19.902966: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:19.902966:   - 设备句柄: 1495381146272
2025-08-26 09:26:19.902966:   - FetchRecords返回值: 0
2025-08-26 09:26:19.902966:   - 报告数量: 2
2025-08-26 09:26:19.902966:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:19.903967:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:19.903967:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:19.903967:   - 设备类型: LSGControlCenter
2025-08-26 09:26:19.903967:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:19.903967:   - 数据长度: 8
2025-08-26 09:26:19.903967:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:19.903967:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:19.904966:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:19.904966:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:19.904966:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:19.904966:   - 设备类型: LSGControlCenter
2025-08-26 09:26:19.904966:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:19.904966:   - 数据长度: 8
2025-08-26 09:26:19.904966:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:19.904966:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:19.905966: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:19.905966:   - 发现标签数量: 2
2025-08-26 09:26:19.905966:   - 标签详情:
2025-08-26 09:26:19.905966:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:19.905966:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:19.905966: RFID扫描: 发现 2 个标签
2025-08-26 09:26:19.905966: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:19.905966: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:19.906966: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:19.906966:   - UID: E004015305F83C8E
2025-08-26 09:26:19.906966:   - Data: E004015305F83C8E
2025-08-26 09:26:19.906966:   - EventType: 1
2025-08-26 09:26:19.906966:   - Direction: 0
2025-08-26 09:26:19.906966:   - Antenna: 1
2025-08-26 09:26:19.906966:   - TagFrequency: 0
2025-08-26 09:26:19.906966: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:19.907966:   - UID: E004015305F83C8E
2025-08-26 09:26:19.907966:   - Data: E004015305F83C8E
2025-08-26 09:26:19.907966:   - EventType: 1
2025-08-26 09:26:19.907966:   - Direction: 0
2025-08-26 09:26:19.907966:   - Antenna: 1
2025-08-26 09:26:19.907966:   - TagFrequency: 0
2025-08-26 09:26:20.400958: 🔄 开始RFID轮询检查...
2025-08-26 09:26:20.400958: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:20.400958: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:20.400958: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:20.401958: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:20.402958: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:20.402958:   - 设备句柄: 1495381146272
2025-08-26 09:26:20.402958:   - FetchRecords返回值: 0
2025-08-26 09:26:20.402958:   - 报告数量: 2
2025-08-26 09:26:20.402958:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:20.403958:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:20.403958:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:20.403958:   - 设备类型: LSGControlCenter
2025-08-26 09:26:20.403958:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:20.403958:   - 数据长度: 8
2025-08-26 09:26:20.403958:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:20.403958:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:20.404957:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:20.404957:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:20.404957:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:20.404957:   - 设备类型: LSGControlCenter
2025-08-26 09:26:20.404957:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:20.404957:   - 数据长度: 8
2025-08-26 09:26:20.404957:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:20.404957:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:20.405957: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:20.405957:   - 发现标签数量: 2
2025-08-26 09:26:20.405957:   - 标签详情:
2025-08-26 09:26:20.405957:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:20.405957:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:20.405957: RFID扫描: 发现 2 个标签
2025-08-26 09:26:20.405957: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:20.405957: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:20.406957: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:20.406957:   - UID: E004015305F83C8E
2025-08-26 09:26:20.406957:   - Data: E004015305F83C8E
2025-08-26 09:26:20.406957:   - EventType: 1
2025-08-26 09:26:20.406957:   - Direction: 0
2025-08-26 09:26:20.406957:   - Antenna: 1
2025-08-26 09:26:20.406957:   - TagFrequency: 0
2025-08-26 09:26:20.406957: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:20.406957:   - UID: E004015305F83C8E
2025-08-26 09:26:20.407958:   - Data: E004015305F83C8E
2025-08-26 09:26:20.407958:   - EventType: 1
2025-08-26 09:26:20.407958:   - Direction: 0
2025-08-26 09:26:20.407958:   - Antenna: 1
2025-08-26 09:26:20.407958:   - TagFrequency: 0
2025-08-26 09:26:20.900949: 🔄 开始RFID轮询检查...
2025-08-26 09:26:20.900949: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:20.901950: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:20.901950: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:20.901950: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:20.902949: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:20.902949:   - 设备句柄: 1495381146272
2025-08-26 09:26:20.902949:   - FetchRecords返回值: 0
2025-08-26 09:26:20.903949:   - 报告数量: 2
2025-08-26 09:26:20.903949:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:20.903949:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:20.903949:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:20.903949:   - 设备类型: LSGControlCenter
2025-08-26 09:26:20.903949:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:20.904950:   - 数据长度: 8
2025-08-26 09:26:20.904950:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:20.904950:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:20.905950:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:20.905950:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:20.905950:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:20.905950:   - 设备类型: LSGControlCenter
2025-08-26 09:26:20.905950:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:20.905950:   - 数据长度: 8
2025-08-26 09:26:20.906991:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:20.906991:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:20.907950: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:20.907950:   - 发现标签数量: 2
2025-08-26 09:26:20.907950:   - 标签详情:
2025-08-26 09:26:20.907950:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:20.907950:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:20.908949: RFID扫描: 发现 2 个标签
2025-08-26 09:26:20.908949: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:20.908949: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:20.908949: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:20.908949:   - UID: E004015305F83C8E
2025-08-26 09:26:20.908949:   - Data: E004015305F83C8E
2025-08-26 09:26:20.909950:   - EventType: 1
2025-08-26 09:26:20.909950:   - Direction: 0
2025-08-26 09:26:20.909950:   - Antenna: 1
2025-08-26 09:26:20.909950:   - TagFrequency: 0
2025-08-26 09:26:20.909950: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:20.909950:   - UID: E004015305F83C8E
2025-08-26 09:26:20.909950:   - Data: E004015305F83C8E
2025-08-26 09:26:20.910949:   - EventType: 1
2025-08-26 09:26:20.910949:   - Direction: 0
2025-08-26 09:26:20.910949:   - Antenna: 1
2025-08-26 09:26:20.910949:   - TagFrequency: 0
2025-08-26 09:26:21.400940: 🔄 开始RFID轮询检查...
2025-08-26 09:26:21.400940: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:21.400940: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:21.400940: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:21.400940: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:21.402940: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:21.402940:   - 设备句柄: 1495381146272
2025-08-26 09:26:21.402940:   - FetchRecords返回值: 0
2025-08-26 09:26:21.402940:   - 报告数量: 2
2025-08-26 09:26:21.402940:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:21.402940:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:21.403940:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:21.403940:   - 设备类型: LSGControlCenter
2025-08-26 09:26:21.403940:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:21.403940:   - 数据长度: 8
2025-08-26 09:26:21.403940:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:21.403940:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:21.404940:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:21.404940:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:21.404940:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:21.404940:   - 设备类型: LSGControlCenter
2025-08-26 09:26:21.404940:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:21.404940:   - 数据长度: 8
2025-08-26 09:26:21.404940:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:21.404940:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:21.405940: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:21.405940:   - 发现标签数量: 2
2025-08-26 09:26:21.405940:   - 标签详情:
2025-08-26 09:26:21.405940:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:21.405940:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:21.405940: RFID扫描: 发现 2 个标签
2025-08-26 09:26:21.405940: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:21.405940: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:21.406940: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:21.406940:   - UID: E004015305F83C8E
2025-08-26 09:26:21.406940:   - Data: E004015305F83C8E
2025-08-26 09:26:21.406940:   - EventType: 1
2025-08-26 09:26:21.406940:   - Direction: 0
2025-08-26 09:26:21.406940:   - Antenna: 1
2025-08-26 09:26:21.406940:   - TagFrequency: 0
2025-08-26 09:26:21.407940: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:21.407940:   - UID: E004015305F83C8E
2025-08-26 09:26:21.407940:   - Data: E004015305F83C8E
2025-08-26 09:26:21.407940:   - EventType: 1
2025-08-26 09:26:21.407940:   - Direction: 0
2025-08-26 09:26:21.407940:   - Antenna: 1
2025-08-26 09:26:21.407940:   - TagFrequency: 0
2025-08-26 09:26:21.900936: 🔄 开始RFID轮询检查...
2025-08-26 09:26:21.901933: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:21.902938: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:21.902938: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:21.902938: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:21.903938: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:21.903938:   - 设备句柄: 1495381146272
2025-08-26 09:26:21.903938:   - FetchRecords返回值: 0
2025-08-26 09:26:21.903938:   - 报告数量: 2
2025-08-26 09:26:21.904932:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:21.904932:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:21.904932:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:21.904932:   - 设备类型: LSGControlCenter
2025-08-26 09:26:21.904932:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:21.904932:   - 数据长度: 8
2025-08-26 09:26:21.905931:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:21.905931:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:21.905931:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:21.905931:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:21.905931:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:21.905931:   - 设备类型: LSGControlCenter
2025-08-26 09:26:21.906932:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:21.906932:   - 数据长度: 8
2025-08-26 09:26:21.906932:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:21.906932:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:21.906932: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:21.906932:   - 发现标签数量: 2
2025-08-26 09:26:21.907931:   - 标签详情:
2025-08-26 09:26:21.907931:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:21.907931:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:21.907931: RFID扫描: 发现 2 个标签
2025-08-26 09:26:21.907931: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:21.907931: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:21.907931: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:21.907931:   - UID: E004015305F83C8E
2025-08-26 09:26:21.908932:   - Data: E004015305F83C8E
2025-08-26 09:26:21.908932:   - EventType: 1
2025-08-26 09:26:21.908932:   - Direction: 0
2025-08-26 09:26:21.908932:   - Antenna: 1
2025-08-26 09:26:21.908932:   - TagFrequency: 0
2025-08-26 09:26:21.908932: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:21.908932:   - UID: E004015305F83C8E
2025-08-26 09:26:21.908932:   - Data: E004015305F83C8E
2025-08-26 09:26:21.909931:   - EventType: 1
2025-08-26 09:26:21.909931:   - Direction: 0
2025-08-26 09:26:21.909931:   - Antenna: 1
2025-08-26 09:26:21.909931:   - TagFrequency: 0
2025-08-26 09:26:22.325924: 接收到数据: aa 00 c9 80 00 00 26 7e
2025-08-26 09:26:22.325924: 🔍 接收到串口数据: aa 00 c9 80 00 00 26 7e
2025-08-26 09:26:22.325924: 🔍 数据长度: 8 字节
2025-08-26 09:26:22.326929: 🔍 预定义命令列表:
2025-08-26 09:26:22.326929:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 09:26:22.326929:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 09:26:22.326929:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 09:26:22.326929:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 09:26:22.326929:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 09:26:22.326929:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 09:26:22.326929:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 09:26:22.326929:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 09:26:22.327924:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 09:26:22.327924:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 09:26:22.327924: ✅ 解析到闸机命令: GateCommand.exitEnd
2025-08-26 09:26:22.327924: 解析到闸机命令: exit_end (出馆结束)
2025-08-26 09:26:22.327924: 收到闸机命令: exit_end (出馆结束)
2025-08-26 09:26:22.327924: 出馆流程结束
2025-08-26 09:26:22.327924: 闸机状态变更: GateState.exitScanning -> GateState.idle
2025-08-26 09:26:22.327924: 闸机状态更新: GateState.exitScanning -> GateState.idle
2025-08-26 09:26:22.328924: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-26 09:26:22.328924: [channel_1] 收到闸机事件: state_changed
2025-08-26 09:26:22.328924: 📨 收到GateCoordinator事件: state_changed
2025-08-26 09:26:22.328924: 闸机状态变更: GateState.idle
2025-08-26 09:26:22.328924: 🎨 处理状态变更UI: idle
2025-08-26 09:26:22.328924: 页面状态变更: SilencePageState.welcome
2025-08-26 09:26:22.328924: [channel_1] 收到闸机事件: exit_end
2025-08-26 09:26:22.328924: [channel_1] 主从机扩展：处理出馆结束
2025-08-26 09:26:22.328924: [channel_1] 清空处理队列，当前大小: 0
2025-08-26 09:26:22.329924: [channel_1] 处理队列已清空
2025-08-26 09:26:22.329924: 📨 收到GateCoordinator事件: exit_end
2025-08-26 09:26:22.329924: 页面状态变更: SilencePageState.welcome
2025-08-26 09:26:22.329924: [channel_1] 通知收集到的条码: []
2025-08-26 09:26:22.329924: ✅ [channel_1] 数据流通知发送成功: 0个条码
2025-08-26 09:26:22.399923: 🔄 开始RFID轮询检查...
2025-08-26 09:26:22.399923: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:22.400924: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:22.400924: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:22.400924: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:22.402928: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:22.402928:   - 设备句柄: 1495381146272
2025-08-26 09:26:22.402928:   - FetchRecords返回值: 0
2025-08-26 09:26:22.403924:   - 报告数量: 2
2025-08-26 09:26:22.403924:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:22.403924:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:22.403924:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:22.403924:   - 设备类型: LSGControlCenter
2025-08-26 09:26:22.404951:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:22.404951:   - 数据长度: 8
2025-08-26 09:26:22.404951:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:22.404951:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:22.405923:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:22.405923:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:22.405923:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:22.405923:   - 设备类型: LSGControlCenter
2025-08-26 09:26:22.405923:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:22.405923:   - 数据长度: 8
2025-08-26 09:26:22.406924:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:22.406924:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:22.406924: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:22.406924:   - 发现标签数量: 2
2025-08-26 09:26:22.406924:   - 标签详情:
2025-08-26 09:26:22.406924:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:22.407923:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:22.407923: RFID扫描: 发现 2 个标签
2025-08-26 09:26:22.407923: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:22.407923: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:22.407923: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:22.407923:   - UID: E004015305F83C8E
2025-08-26 09:26:22.407923:   - Data: E004015305F83C8E
2025-08-26 09:26:22.408923:   - EventType: 1
2025-08-26 09:26:22.408923:   - Direction: 0
2025-08-26 09:26:22.408923:   - Antenna: 1
2025-08-26 09:26:22.408923:   - TagFrequency: 0
2025-08-26 09:26:22.408923: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:22.409924:   - UID: E004015305F83C8E
2025-08-26 09:26:22.409924:   - Data: E004015305F83C8E
2025-08-26 09:26:22.409924:   - EventType: 1
2025-08-26 09:26:22.409924:   - Direction: 0
2025-08-26 09:26:22.409924:   - Antenna: 1
2025-08-26 09:26:22.409924:   - TagFrequency: 0
2025-08-26 09:26:22.899914: 🔄 开始RFID轮询检查...
2025-08-26 09:26:22.899914: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:22.899914: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:22.899914: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:22.899914: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:22.902914: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:22.903926:   - 设备句柄: 1495381146272
2025-08-26 09:26:22.903926:   - FetchRecords返回值: 0
2025-08-26 09:26:22.903926:   - 报告数量: 2
2025-08-26 09:26:22.904914:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:22.904914:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:22.904914:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:22.904914:   - 设备类型: LSGControlCenter
2025-08-26 09:26:22.904914:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:22.905914:   - 数据长度: 8
2025-08-26 09:26:22.905914:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:22.905914:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:22.905914:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:22.905914:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:22.906914:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:22.906914:   - 设备类型: LSGControlCenter
2025-08-26 09:26:22.906914:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:22.906914:   - 数据长度: 8
2025-08-26 09:26:22.906914:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:22.906914:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:22.907914: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:22.907914:   - 发现标签数量: 2
2025-08-26 09:26:22.907914:   - 标签详情:
2025-08-26 09:26:22.907914:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:22.907914:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:22.907914: RFID扫描: 发现 2 个标签
2025-08-26 09:26:22.908914: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:22.908914: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:22.908914: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:22.908914:   - UID: E004015305F83C8E
2025-08-26 09:26:22.908914:   - Data: E004015305F83C8E
2025-08-26 09:26:22.908914:   - EventType: 1
2025-08-26 09:26:22.908914:   - Direction: 0
2025-08-26 09:26:22.909914:   - Antenna: 1
2025-08-26 09:26:22.909914:   - TagFrequency: 0
2025-08-26 09:26:22.909914: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:22.909914:   - UID: E004015305F83C8E
2025-08-26 09:26:22.909914:   - Data: E004015305F83C8E
2025-08-26 09:26:22.909914:   - EventType: 1
2025-08-26 09:26:22.909914:   - Direction: 0
2025-08-26 09:26:22.909914:   - Antenna: 1
2025-08-26 09:26:22.910914:   - TagFrequency: 0
2025-08-26 09:26:23.400905: 🔄 开始RFID轮询检查...
2025-08-26 09:26:23.400905: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:23.400905: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:23.400905: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:23.400905: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:23.402905: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:23.402905:   - 设备句柄: 1495381146272
2025-08-26 09:26:23.402905:   - FetchRecords返回值: 0
2025-08-26 09:26:23.403906:   - 报告数量: 2
2025-08-26 09:26:23.403906:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:23.403906:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:23.403906:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:23.404906:   - 设备类型: LSGControlCenter
2025-08-26 09:26:23.404906:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:23.404906:   - 数据长度: 8
2025-08-26 09:26:23.404906:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:23.404906:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:23.405906:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:23.405906:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:23.405906:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:23.405906:   - 设备类型: LSGControlCenter
2025-08-26 09:26:23.405906:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:23.405906:   - 数据长度: 8
2025-08-26 09:26:23.405906:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:23.406905:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:23.406905: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:23.406905:   - 发现标签数量: 2
2025-08-26 09:26:23.406905:   - 标签详情:
2025-08-26 09:26:23.406905:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:23.407906:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:23.407906: RFID扫描: 发现 2 个标签
2025-08-26 09:26:23.407906: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:23.407906: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:23.407906: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:23.407906:   - UID: E004015305F83C8E
2025-08-26 09:26:23.407906:   - Data: E004015305F83C8E
2025-08-26 09:26:23.408905:   - EventType: 1
2025-08-26 09:26:23.408905:   - Direction: 0
2025-08-26 09:26:23.408905:   - Antenna: 1
2025-08-26 09:26:23.408905:   - TagFrequency: 0
2025-08-26 09:26:23.408905: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:23.408905:   - UID: E004015305F83C8E
2025-08-26 09:26:23.408905:   - Data: E004015305F83C8E
2025-08-26 09:26:23.409905:   - EventType: 1
2025-08-26 09:26:23.409905:   - Direction: 0
2025-08-26 09:26:23.409905:   - Antenna: 1
2025-08-26 09:26:23.409905:   - TagFrequency: 0
2025-08-26 09:26:23.900897: 🔄 开始RFID轮询检查...
2025-08-26 09:26:23.900897: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:23.900897: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:23.900897: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:23.901897: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:23.902897: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:23.902897:   - 设备句柄: 1495381146272
2025-08-26 09:26:23.902897:   - FetchRecords返回值: 0
2025-08-26 09:26:23.902897:   - 报告数量: 2
2025-08-26 09:26:23.902897:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:23.902897:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:23.903897:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:23.903897:   - 设备类型: LSGControlCenter
2025-08-26 09:26:23.903897:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:23.903897:   - 数据长度: 8
2025-08-26 09:26:23.903897:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:23.903897:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:23.903897:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:23.904896:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:23.904896:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:23.904896:   - 设备类型: LSGControlCenter
2025-08-26 09:26:23.904896:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:23.904896:   - 数据长度: 8
2025-08-26 09:26:23.904896:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:23.904896:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:23.904896: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:23.905896:   - 发现标签数量: 2
2025-08-26 09:26:23.905896:   - 标签详情:
2025-08-26 09:26:23.905896:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:23.905896:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:23.905896: RFID扫描: 发现 2 个标签
2025-08-26 09:26:23.905896: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:23.906910: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:23.906910: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:23.907898:   - UID: E004015305F83C8E
2025-08-26 09:26:23.907898:   - Data: E004015305F83C8E
2025-08-26 09:26:23.907898:   - EventType: 1
2025-08-26 09:26:23.908901:   - Direction: 0
2025-08-26 09:26:23.908901:   - Antenna: 1
2025-08-26 09:26:23.908901:   - TagFrequency: 0
2025-08-26 09:26:23.909898: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:23.909898:   - UID: E004015305F83C8E
2025-08-26 09:26:23.909898:   - Data: E004015305F83C8E
2025-08-26 09:26:23.909898:   - EventType: 1
2025-08-26 09:26:23.910897:   - Direction: 0
2025-08-26 09:26:23.910897:   - Antenna: 1
2025-08-26 09:26:23.910897:   - TagFrequency: 0
2025-08-26 09:26:24.400888: 🔄 开始RFID轮询检查...
2025-08-26 09:26:24.400888: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:24.400888: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:24.400888: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:24.400888: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:24.402888: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:24.402888:   - 设备句柄: 1495381146272
2025-08-26 09:26:24.402888:   - FetchRecords返回值: 0
2025-08-26 09:26:24.402888:   - 报告数量: 2
2025-08-26 09:26:24.402888:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:24.402888:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:24.403888:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:24.403888:   - 设备类型: LSGControlCenter
2025-08-26 09:26:24.403888:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:24.403888:   - 数据长度: 8
2025-08-26 09:26:24.403888:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:24.403888:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:24.403888:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:24.404888:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:24.404888:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:24.404888:   - 设备类型: LSGControlCenter
2025-08-26 09:26:24.404888:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:24.404888:   - 数据长度: 8
2025-08-26 09:26:24.404888:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:24.404888:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:24.404888: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:24.405888:   - 发现标签数量: 2
2025-08-26 09:26:24.405888:   - 标签详情:
2025-08-26 09:26:24.405888:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:24.405888:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:24.405888: RFID扫描: 发现 2 个标签
2025-08-26 09:26:24.405888: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:24.405888: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:24.406888: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:24.406888:   - UID: E004015305F83C8E
2025-08-26 09:26:24.406888:   - Data: E004015305F83C8E
2025-08-26 09:26:24.406888:   - EventType: 1
2025-08-26 09:26:24.406888:   - Direction: 0
2025-08-26 09:26:24.406888:   - Antenna: 1
2025-08-26 09:26:24.406888:   - TagFrequency: 0
2025-08-26 09:26:24.406888: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:24.406888:   - UID: E004015305F83C8E
2025-08-26 09:26:24.407888:   - Data: E004015305F83C8E
2025-08-26 09:26:24.407888:   - EventType: 1
2025-08-26 09:26:24.407888:   - Direction: 0
2025-08-26 09:26:24.407888:   - Antenna: 1
2025-08-26 09:26:24.407888:   - TagFrequency: 0
2025-08-26 09:26:24.900879: 🔄 开始RFID轮询检查...
2025-08-26 09:26:24.900879: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:24.901885: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:24.901885: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:24.901885: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:24.902880: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:24.902880:   - 设备句柄: 1495381146272
2025-08-26 09:26:24.903879:   - FetchRecords返回值: 0
2025-08-26 09:26:24.903879:   - 报告数量: 2
2025-08-26 09:26:24.903879:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:24.903879:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:24.903879:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:24.904883:   - 设备类型: LSGControlCenter
2025-08-26 09:26:24.904883:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:24.904883:   - 数据长度: 8
2025-08-26 09:26:24.905880:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:24.905880:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:24.905880:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:24.905880:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:24.905880:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:24.906889:   - 设备类型: LSGControlCenter
2025-08-26 09:26:24.906889:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:24.906889:   - 数据长度: 8
2025-08-26 09:26:24.906889:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:24.907880:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:24.907880: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:24.907880:   - 发现标签数量: 2
2025-08-26 09:26:24.907880:   - 标签详情:
2025-08-26 09:26:24.907880:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:24.908880:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:24.908880: RFID扫描: 发现 2 个标签
2025-08-26 09:26:24.908880: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:24.908880: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:24.908880: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:24.909879:   - UID: E004015305F83C8E
2025-08-26 09:26:24.909879:   - Data: E004015305F83C8E
2025-08-26 09:26:24.909879:   - EventType: 1
2025-08-26 09:26:24.909879:   - Direction: 0
2025-08-26 09:26:24.909879:   - Antenna: 1
2025-08-26 09:26:24.909879:   - TagFrequency: 0
2025-08-26 09:26:24.909879: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:24.910879:   - UID: E004015305F83C8E
2025-08-26 09:26:24.910879:   - Data: E004015305F83C8E
2025-08-26 09:26:24.910879:   - EventType: 1
2025-08-26 09:26:24.910879:   - Direction: 0
2025-08-26 09:26:24.910879:   - Antenna: 1
2025-08-26 09:26:24.910879:   - TagFrequency: 0
2025-08-26 09:26:25.400870: 🔄 开始RFID轮询检查...
2025-08-26 09:26:25.400870: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:25.400870: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:25.400870: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:25.401871: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:25.402871: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:25.402871:   - 设备句柄: 1495381146272
2025-08-26 09:26:25.402871:   - FetchRecords返回值: 0
2025-08-26 09:26:25.402871:   - 报告数量: 2
2025-08-26 09:26:25.402871:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:25.403871:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:25.403871:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:25.403871:   - 设备类型: LSGControlCenter
2025-08-26 09:26:25.403871:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:25.403871:   - 数据长度: 8
2025-08-26 09:26:25.403871:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:25.403871:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:25.403871:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:25.404870:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:25.404870:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:25.404870:   - 设备类型: LSGControlCenter
2025-08-26 09:26:25.404870:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:25.404870:   - 数据长度: 8
2025-08-26 09:26:25.404870:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:25.404870:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:25.404870: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:25.405870:   - 发现标签数量: 2
2025-08-26 09:26:25.405870:   - 标签详情:
2025-08-26 09:26:25.405870:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:25.405870:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:25.405870: RFID扫描: 发现 2 个标签
2025-08-26 09:26:25.405870: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:25.405870: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:25.405870: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:25.406870:   - UID: E004015305F83C8E
2025-08-26 09:26:25.406870:   - Data: E004015305F83C8E
2025-08-26 09:26:25.406870:   - EventType: 1
2025-08-26 09:26:25.406870:   - Direction: 0
2025-08-26 09:26:25.406870:   - Antenna: 1
2025-08-26 09:26:25.406870:   - TagFrequency: 0
2025-08-26 09:26:25.406870: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:25.406870:   - UID: E004015305F83C8E
2025-08-26 09:26:25.407870:   - Data: E004015305F83C8E
2025-08-26 09:26:25.407870:   - EventType: 1
2025-08-26 09:26:25.407870:   - Direction: 0
2025-08-26 09:26:25.407870:   - Antenna: 1
2025-08-26 09:26:25.407870:   - TagFrequency: 0
2025-08-26 09:26:25.900862: 🔄 开始RFID轮询检查...
2025-08-26 09:26:25.900862: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:25.900862: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:25.900862: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:25.900862: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:25.902862: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:25.902862:   - 设备句柄: 1495381146272
2025-08-26 09:26:25.902862:   - FetchRecords返回值: 0
2025-08-26 09:26:25.902862:   - 报告数量: 2
2025-08-26 09:26:25.902862:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:25.903862:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:25.903862:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:25.903862:   - 设备类型: LSGControlCenter
2025-08-26 09:26:25.903862:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:25.903862:   - 数据长度: 8
2025-08-26 09:26:25.903862:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:25.903862:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:25.904862:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:25.904862:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:25.904862:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:25.904862:   - 设备类型: LSGControlCenter
2025-08-26 09:26:25.904862:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:25.904862:   - 数据长度: 8
2025-08-26 09:26:25.904862:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:25.904862:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:25.905862: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:25.905862:   - 发现标签数量: 2
2025-08-26 09:26:25.905862:   - 标签详情:
2025-08-26 09:26:25.905862:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:25.905862:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:25.905862: RFID扫描: 发现 2 个标签
2025-08-26 09:26:25.905862: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:25.905862: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:25.906862: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:25.906862:   - UID: E004015305F83C8E
2025-08-26 09:26:25.906862:   - Data: E004015305F83C8E
2025-08-26 09:26:25.906862:   - EventType: 1
2025-08-26 09:26:25.906862:   - Direction: 0
2025-08-26 09:26:25.906862:   - Antenna: 1
2025-08-26 09:26:25.906862:   - TagFrequency: 0
2025-08-26 09:26:25.906862: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:25.906862:   - UID: E004015305F83C8E
2025-08-26 09:26:25.907861:   - Data: E004015305F83C8E
2025-08-26 09:26:25.907861:   - EventType: 1
2025-08-26 09:26:25.907861:   - Direction: 0
2025-08-26 09:26:25.907861:   - Antenna: 1
2025-08-26 09:26:25.907861:   - TagFrequency: 0
2025-08-26 09:26:26.400853: 🔄 开始RFID轮询检查...
2025-08-26 09:26:26.400853: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:26.400853: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:26.400853: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:26.400853: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:26.402853: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:26.402853:   - 设备句柄: 1495381146272
2025-08-26 09:26:26.402853:   - FetchRecords返回值: 0
2025-08-26 09:26:26.402853:   - 报告数量: 2
2025-08-26 09:26:26.402853:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:26.403853:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:26.403853:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:26.403853:   - 设备类型: LSGControlCenter
2025-08-26 09:26:26.403853:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:26.403853:   - 数据长度: 8
2025-08-26 09:26:26.403853:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:26.403853:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:26.404853:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:26.404853:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:26.404853:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:26.404853:   - 设备类型: LSGControlCenter
2025-08-26 09:26:26.404853:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:26.404853:   - 数据长度: 8
2025-08-26 09:26:26.404853:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:26.404853:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:26.405853: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:26.405853:   - 发现标签数量: 2
2025-08-26 09:26:26.405853:   - 标签详情:
2025-08-26 09:26:26.405853:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:26.405853:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:26.405853: RFID扫描: 发现 2 个标签
2025-08-26 09:26:26.405853: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:26.405853: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:26.406853: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:26.406853:   - UID: E004015305F83C8E
2025-08-26 09:26:26.406853:   - Data: E004015305F83C8E
2025-08-26 09:26:26.406853:   - EventType: 1
2025-08-26 09:26:26.406853:   - Direction: 0
2025-08-26 09:26:26.406853:   - Antenna: 1
2025-08-26 09:26:26.406853:   - TagFrequency: 0
2025-08-26 09:26:26.406853: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:26.407853:   - UID: E004015305F83C8E
2025-08-26 09:26:26.407853:   - Data: E004015305F83C8E
2025-08-26 09:26:26.407853:   - EventType: 1
2025-08-26 09:26:26.407853:   - Direction: 0
2025-08-26 09:26:26.407853:   - Antenna: 1
2025-08-26 09:26:26.407853:   - TagFrequency: 0
2025-08-26 09:26:26.900844: 🔄 开始RFID轮询检查...
2025-08-26 09:26:26.900844: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:26.900844: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:26.900844: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:26.900844: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:26.902844: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:26.902844:   - 设备句柄: 1495381146272
2025-08-26 09:26:26.902844:   - FetchRecords返回值: 0
2025-08-26 09:26:26.902844:   - 报告数量: 2
2025-08-26 09:26:26.902844:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:26.902844:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:26.903844:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:26.903844:   - 设备类型: LSGControlCenter
2025-08-26 09:26:26.903844:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:26.903844:   - 数据长度: 8
2025-08-26 09:26:26.903844:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:26.903844:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:26.903844:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:26.904844:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:26.904844:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:26.904844:   - 设备类型: LSGControlCenter
2025-08-26 09:26:26.904844:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:26.904844:   - 数据长度: 8
2025-08-26 09:26:26.904844:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:26.904844:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:26.905844: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:26.905844:   - 发现标签数量: 2
2025-08-26 09:26:26.905844:   - 标签详情:
2025-08-26 09:26:26.905844:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:26.905844:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:26.905844: RFID扫描: 发现 2 个标签
2025-08-26 09:26:26.905844: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:26.905844: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:26.905844: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:26.906844:   - UID: E004015305F83C8E
2025-08-26 09:26:26.906844:   - Data: E004015305F83C8E
2025-08-26 09:26:26.906844:   - EventType: 1
2025-08-26 09:26:26.906844:   - Direction: 0
2025-08-26 09:26:26.906844:   - Antenna: 1
2025-08-26 09:26:26.906844:   - TagFrequency: 0
2025-08-26 09:26:26.906844: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:26.906844:   - UID: E004015305F83C8E
2025-08-26 09:26:26.907844:   - Data: E004015305F83C8E
2025-08-26 09:26:26.907844:   - EventType: 1
2025-08-26 09:26:26.907844:   - Direction: 0
2025-08-26 09:26:26.907844:   - Antenna: 1
2025-08-26 09:26:26.907844:   - TagFrequency: 0
2025-08-26 09:26:27.400835: 🔄 开始RFID轮询检查...
2025-08-26 09:26:27.400835: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:27.400835: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:27.400835: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:27.401841: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:27.402836: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:27.402836:   - 设备句柄: 1495381146272
2025-08-26 09:26:27.402836:   - FetchRecords返回值: 0
2025-08-26 09:26:27.402836:   - 报告数量: 2
2025-08-26 09:26:27.402836:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:27.402836:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:27.403836:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:27.403836:   - 设备类型: LSGControlCenter
2025-08-26 09:26:27.403836:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:27.403836:   - 数据长度: 8
2025-08-26 09:26:27.403836:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:27.403836:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:27.403836:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:27.403836:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:27.404836:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:27.404836:   - 设备类型: LSGControlCenter
2025-08-26 09:26:27.404836:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:27.404836:   - 数据长度: 8
2025-08-26 09:26:27.404836:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:27.404836:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:27.404836: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:27.405836:   - 发现标签数量: 2
2025-08-26 09:26:27.405836:   - 标签详情:
2025-08-26 09:26:27.405836:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:27.405836:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:27.405836: RFID扫描: 发现 2 个标签
2025-08-26 09:26:27.405836: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:27.405836: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:27.405836: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:27.406836:   - UID: E004015305F83C8E
2025-08-26 09:26:27.406836:   - Data: E004015305F83C8E
2025-08-26 09:26:27.406836:   - EventType: 1
2025-08-26 09:26:27.406836:   - Direction: 0
2025-08-26 09:26:27.406836:   - Antenna: 1
2025-08-26 09:26:27.406836:   - TagFrequency: 0
2025-08-26 09:26:27.406836: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:27.406836:   - UID: E004015305F83C8E
2025-08-26 09:26:27.407836:   - Data: E004015305F83C8E
2025-08-26 09:26:27.407836:   - EventType: 1
2025-08-26 09:26:27.407836:   - Direction: 0
2025-08-26 09:26:27.407836:   - Antenna: 1
2025-08-26 09:26:27.407836:   - TagFrequency: 0
2025-08-26 09:26:27.900827: 🔄 开始RFID轮询检查...
2025-08-26 09:26:27.900827: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:27.900827: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:27.900827: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:27.900827: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:27.902827: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:27.902827:   - 设备句柄: 1495381146272
2025-08-26 09:26:27.902827:   - FetchRecords返回值: 0
2025-08-26 09:26:27.902827:   - 报告数量: 2
2025-08-26 09:26:27.902827:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:27.902827:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:27.903827:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:27.903827:   - 设备类型: LSGControlCenter
2025-08-26 09:26:27.903827:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:27.903827:   - 数据长度: 8
2025-08-26 09:26:27.903827:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:27.903827:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:27.904830:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:27.904830:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:27.905832:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:27.905832:   - 设备类型: LSGControlCenter
2025-08-26 09:26:27.906829:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:27.906829:   - 数据长度: 8
2025-08-26 09:26:27.906829:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:27.907828:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:27.907828: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:27.907828:   - 发现标签数量: 2
2025-08-26 09:26:27.907828:   - 标签详情:
2025-08-26 09:26:27.908827:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:27.908827:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:27.908827: RFID扫描: 发现 2 个标签
2025-08-26 09:26:27.908827: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:27.908827: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:27.909827: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:27.909827:   - UID: E004015305F83C8E
2025-08-26 09:26:27.909827:   - Data: E004015305F83C8E
2025-08-26 09:26:27.909827:   - EventType: 1
2025-08-26 09:26:27.909827:   - Direction: 0
2025-08-26 09:26:27.910827:   - Antenna: 1
2025-08-26 09:26:27.910827:   - TagFrequency: 0
2025-08-26 09:26:27.910827: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:27.910827:   - UID: E004015305F83C8E
2025-08-26 09:26:27.910827:   - Data: E004015305F83C8E
2025-08-26 09:26:27.910827:   - EventType: 1
2025-08-26 09:26:27.911827:   - Direction: 0
2025-08-26 09:26:27.911827:   - Antenna: 1
2025-08-26 09:26:27.911827:   - TagFrequency: 0
2025-08-26 09:26:28.400818: 🔄 开始RFID轮询检查...
2025-08-26 09:26:28.400818: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 09:26:28.400818: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 09:26:28.400818: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 09:26:28.400818: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 09:26:28.402818: 🔍 LSGate硬件扫描详情:
2025-08-26 09:26:28.402818:   - 设备句柄: 1495381146272
2025-08-26 09:26:28.402818:   - FetchRecords返回值: 0
2025-08-26 09:26:28.402818:   - 报告数量: 2
2025-08-26 09:26:28.402818:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:28.402818:   - 原始数据: 01 00 19 08 1A 09 1B 0B 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:28.403818:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:28.403818:   - 设备类型: LSGControlCenter
2025-08-26 09:26:28.403818:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:28.403818:   - 数据长度: 8
2025-08-26 09:26:28.403818:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:28.403818:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:28.403818:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 09:26:28.403818:   - 原始数据: 01 00 19 08 1A 09 1B 0D 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 09:26:28.404818:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 09:26:28.404818:   - 设备类型: LSGControlCenter
2025-08-26 09:26:28.404818:   - 事件类型: 1, 方向: 0
2025-08-26 09:26:28.404818:   - 数据长度: 8
2025-08-26 09:26:28.404818:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 09:26:28.404818:   - 提取的UID: E004015305F83C8E
2025-08-26 09:26:28.404818: 📊 LSGate扫描结果汇总:
2025-08-26 09:26:28.404818:   - 发现标签数量: 2
2025-08-26 09:26:28.405818:   - 标签详情:
2025-08-26 09:26:28.405818:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:28.405818:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 09:26:28.405818: RFID扫描: 发现 2 个标签
2025-08-26 09:26:28.405818: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 09:26:28.405818: 🎉 LSGate检测到RFID标签！
2025-08-26 09:26:28.405818: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 11], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:28.405818:   - UID: E004015305F83C8E
2025-08-26 09:26:28.406818:   - Data: E004015305F83C8E
2025-08-26 09:26:28.406818:   - EventType: 1
2025-08-26 09:26:28.406818:   - Direction: 0
2025-08-26 09:26:28.406818:   - Antenna: 1
2025-08-26 09:26:28.406818:   - TagFrequency: 0
2025-08-26 09:26:28.406818: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 9, 27, 13], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 09:26:28.406818:   - UID: E004015305F83C8E
2025-08-26 09:26:28.406818:   - Data: E004015305F83C8E
2025-08-26 09:26:28.407818:   - EventType: 1
2025-08-26 09:26:28.407818:   - Direction: 0
2025-08-26 09:26:28.407818:   - Antenna: 1
2025-08-26 09:26:28.407818:   - TagFrequency: 0
